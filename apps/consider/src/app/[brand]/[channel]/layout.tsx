import { draftMode } from 'next/headers';

import type { Tenant } from '@qantasexperiences/tenants';
import { getTenantTitle } from '@qantasexperiences/tenants';
import { TenantProvider } from '@qantasexperiences/tenants/react';
import { getFontClasses } from '@qantasexperiences/theming/fonts';
import { DevStatusBanner, RacRouteProvider } from '@qantasexperiences/ui';

import './global.css';

import { getMaintenanceMessage } from '@qantasexperiences/contentful';
import { serverEnv } from '@qantasexperiences/env/server';
import {
  ConnectedPointsTiersProvider,
  SrAnnouncementsProvider,
} from '@qantasexperiences/ui/providers';

import { Analytics } from './_components/Analytics';
import { LoginEvent } from './_components/LoginEvent';
import { MaintenanceMessage } from './_components/MaintenanceMessage';
import { PageViewEvent } from './_components/PageViewEvent';
import { AuthProvider } from './_providers/AuthProvider';
import { FeatureFlagProvider } from './_providers/FeatureFlagProvider';
import { FetchProvider } from './_providers/FetchProvider';
import { ReactQueryProvider } from './_providers/ReactQueryProvider';
import { getFavIconConfig } from './_utils/getFavIconConfig';

interface Props {
  footer: React.ReactNode;
  header: React.ReactNode;
  params: Promise<Tenant>;
}

export async function generateMetadata({ params }: Props) {
  const { brand, channel } = await params;
  const applicationName = getTenantTitle({ brand, channel });

  return {
    title: {
      template: `%s | ${applicationName}`,
      default: `${applicationName}`,
    },
    icons: getFavIconConfig(brand),
  };
}

export default async function RootLayout({
  children,
  footer,
  header,
  params,
}: React.PropsWithChildren<Props>) {
  const tenant = await params;
  const { isEnabled } = await draftMode();
  const maintenanceMessage = await getMaintenanceMessage({ tenant });

  return (
    <html lang="en" className={getFontClasses(tenant.brand)} data-theme={tenant.brand}>
      <body id="top">
        <SrAnnouncementsProvider>
          <DevStatusBanner isEnabled={isEnabled} />
          <TenantProvider value={tenant}>
            <FeatureFlagProvider tenant={tenant} sdkKey={serverEnv.OPTIMIZELY_SDK_KEY}>
              <AuthProvider>
                <ReactQueryProvider>
                  <FetchProvider tenant={tenant}>
                    <LoginEvent />
                    <ConnectedPointsTiersProvider tenant={tenant}>
                      <RacRouteProvider>
                        {header}
                        {maintenanceMessage ? (
                          <MaintenanceMessage message={maintenanceMessage} />
                        ) : (
                          children
                        )}
                        {footer}
                      </RacRouteProvider>
                    </ConnectedPointsTiersProvider>
                  </FetchProvider>
                </ReactQueryProvider>
              </AuthProvider>
            </FeatureFlagProvider>
            <PageViewEvent />
          </TenantProvider>
          <Analytics tenant={tenant} />
        </SrAnnouncementsProvider>
      </body>
    </html>
  );
}
