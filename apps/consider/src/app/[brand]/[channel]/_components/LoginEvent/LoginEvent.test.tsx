import type { ReadonlyURLSearchParams } from 'next/navigation';
import { usePathname, useRouter } from 'next/navigation';

import { useGetMemberDetails } from '@qantasexperiences/data/client';
import { useSearchParams } from '@qantasexperiences/ui';
import { render } from '@qantasexperiences/ui/test-utils';
import { getCookie } from '@qantasexperiences/utils/browser';

import { sendLoginEvent } from '../../_analytics';
import { LoginEvent } from './LoginEvent';

jest.mock('next/navigation');

jest.mock('@qantasexperiences/data/client', () => ({
  useGetMemberDetails: jest.fn(),
}));
jest.mock('@qantasexperiences/ui', () => ({
  useSearchParams: jest.fn(),
}));
jest.mock('@qantasexperiences/utils/browser', () => ({
  getCookie: jest.fn(),
}));
jest.mock('../../_analytics', () => ({
  sendLoginEvent: jest.fn(),
}));

describe('LoginEvent', () => {
  let mockUpdateSearchParams: jest.Mock;
  const mockUseRouter = jest.mocked(useRouter);
  const mockUsePathname = jest.mocked(usePathname);
  const mockUseGetMemberDetails = jest.mocked(useGetMemberDetails);
  const mockGetCookie = jest.mocked(getCookie);
  const mockSendLoginEvent = jest.mocked(sendLoginEvent);

  beforeEach(() => {
    jest.clearAllMocks();
    mockUpdateSearchParams = jest.fn();
    mockUseRouter.mockReturnValue({
      replace: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      push: jest.fn(),
      prefetch: jest.fn(),
    } as ReturnType<typeof useRouter>);
    mockUsePathname.mockReturnValue('/test');

    jest.mocked(useSearchParams).mockImplementation(() => ({
      searchParams: new URLSearchParams() as ReadonlyURLSearchParams,
      updateSearchParams: mockUpdateSearchParams,
    }));
  });

  it('should fire login event and remove loginSuccess param', () => {
    jest.mocked(useSearchParams).mockImplementation(() => ({
      searchParams: new URLSearchParams('loginSuccess=1&foo=bar') as ReadonlyURLSearchParams,
      updateSearchParams: mockUpdateSearchParams,
    }));
    mockUseGetMemberDetails.mockReturnValue({
      data: { pointBalance: 1234 },
    } as ReturnType<typeof useGetMemberDetails>);
    mockGetCookie.mockImplementation(() => 'hash456');

    render(<LoginEvent />);

    expect(mockSendLoginEvent).toHaveBeenCalledWith({
      userPoints: 1234,
      userQffHash: 'hash456',
    });
    expect(mockUpdateSearchParams).toHaveBeenCalledWith({ loginSuccess: null });
  });

  it('should not fire login event if loginSuccess param is missing', () => {
    mockUseGetMemberDetails.mockReturnValue({
      data: { pointBalance: 1234 },
    } as ReturnType<typeof useGetMemberDetails>);

    render(<LoginEvent />);

    expect(mockSendLoginEvent).not.toHaveBeenCalled();
    expect(mockUpdateSearchParams).not.toHaveBeenCalled();
  });

  it('should not fire login event if user is missing', () => {
    jest.mocked(useSearchParams).mockImplementation(() => ({
      searchParams: new URLSearchParams('loginSuccess=1') as ReadonlyURLSearchParams,
      updateSearchParams: mockUpdateSearchParams,
    }));
    mockUseGetMemberDetails.mockReturnValue({
      data: undefined,
    } as ReturnType<typeof useGetMemberDetails>);

    render(<LoginEvent />);

    expect(mockSendLoginEvent).not.toHaveBeenCalled();
    expect(mockUpdateSearchParams).not.toHaveBeenCalled();
  });

  it('should handle empty cookie value for QF_VALUE_COOKIE_NAME', () => {
    jest.mocked(useSearchParams).mockImplementation(() => ({
      searchParams: new URLSearchParams('loginSuccess=1&foo=bar') as ReadonlyURLSearchParams,
      updateSearchParams: mockUpdateSearchParams,
    }));
    mockUseGetMemberDetails.mockReturnValue({
      data: { pointBalance: 1234 },
    } as ReturnType<typeof useGetMemberDetails>);
    mockGetCookie.mockImplementation(() => null);

    render(<LoginEvent />);

    expect(mockSendLoginEvent).toHaveBeenCalledWith({
      userPoints: 1234,
      userQffHash: '',
    });
    expect(mockUpdateSearchParams).toHaveBeenCalledWith({ loginSuccess: null });
  });
});
