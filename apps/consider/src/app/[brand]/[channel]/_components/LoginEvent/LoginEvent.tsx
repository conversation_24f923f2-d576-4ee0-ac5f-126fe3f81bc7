'use client';

import { useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';

import { QF_VALUE_COOKIE_NAME } from '@qantasexperiences/auth/constants';
import { useGetMemberDetails } from '@qantasexperiences/data/client';
import { useSearchParams } from '@qantasexperiences/ui';
import { getCookie } from '@qantasexperiences/utils/browser';

import { sendLoginEvent } from '../../_analytics';

export const LoginEvent = () => {
  const { data: user } = useGetMemberDetails();
  const { searchParams, updateSearchParams } = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const hasUserJustLoggedIn = searchParams.get('loginSuccess');
    if (user && hasUserJustLoggedIn) {
      const userQffHash = getCookie(QF_VALUE_COOKIE_NAME) ?? '';
      sendLoginEvent({
        userPoints: user.pointBalance,
        userQffHash,
      });
      updateSearchParams({ loginSuccess: null });
    }
  }, [user, pathname, router, searchParams, updateSearchParams]);

  return null;
};
