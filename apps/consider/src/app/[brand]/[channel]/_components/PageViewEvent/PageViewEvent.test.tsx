import { cookies } from 'next/headers';
import { useParams, usePathname } from 'next/navigation';
import { sendGTMEvent } from '@next/third-parties/google';

import { getSessionData } from '@qantasexperiences/auth/server';
import { render } from '@qantasexperiences/ui/test-utils';

import { PageViewEvent } from './PageViewEvent';

jest.mock('crypto');
jest.mock('next/headers');
jest.mock('@next/third-parties/google');
jest.mock('@qantasexperiences/auth/server', () => ({
  getSessionData: jest.fn(),
}));
jest.mock('next/navigation');

describe('PageViewEvent', () => {
  beforeEach(() => {
    jest.mocked(useParams).mockReturnValue({});
    jest.mocked(usePathname).mockReturnValue('/test');
    // @ts-expect-error wants ALL cookies methods implemented (we only use get)
    jest.mocked(cookies).mockResolvedValue({ get: () => undefined });
    jest.mocked(getSessionData).mockResolvedValue(undefined);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders nothing', async () => {
    const { container } = render(await PageViewEvent({}), { channel: 'holidays', brand: 'qantas' });

    expect(container).toBeEmptyDOMElement();
  });

  it('sends page view event with logged out user info', async () => {
    render(await PageViewEvent({}), { channel: 'holidays', brand: 'qantas' });

    expect(sendGTMEvent).toHaveBeenCalledWith(
      expect.objectContaining({
        user: {
          user_id: '',
          user_login_status: 'logged out',
          user_points: undefined,
          user_qff_hash: '',
        },
      }),
    );
  });

  it('sends page view event with user info and user ID as qffHashId when logged in', async () => {
    jest.mocked(getSessionData).mockResolvedValue({
      accessToken: 'accessToken',
      clientId: 'clientId',
      memberId: 'memberId',
      user: {
        memberId: 'memberId',
        firstName: 'firstName',
        lastName: 'lastName',
        membershipTier: 'membershipTier',
        pointBalance: 0,
        title: 'title',
        preferredName: 'preferredName',
      },
    });
    jest.mocked(cookies).mockResolvedValue({
      // @ts-expect-error Not implementing the full response
      get: () => {
        return { value: 'hash-id' };
      },
    });

    render(await PageViewEvent({}), { channel: 'holidays', brand: 'qantas' });

    expect(sendGTMEvent).toHaveBeenCalledWith(
      expect.objectContaining({
        user: {
          user_id: 'hash-id',
          user_login_status: 'logged in',
          user_points: 0,
          user_qff_hash: 'hash-id',
        },
      }),
    );
  });
});
