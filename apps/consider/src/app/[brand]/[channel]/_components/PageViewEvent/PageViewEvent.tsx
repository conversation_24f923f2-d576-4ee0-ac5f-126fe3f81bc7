import type { FC } from 'react';
import { cookies } from 'next/headers';

import { getSessionData } from '@qantasexperiences/auth/server';

import SendPageViewEvent from './_components/SendPageViewEvent';

export const QH_PERSISTENT_USER_ID_COOKIE_NAME = 'qh_user_id';
export const QFF_HASH_ID_COOKIE_NAME = 'QF_VALUE';

export const PageViewEvent: FC = async () => {
  const [qffSession, cookieStore] = await Promise.all([getSessionData(), cookies()]);

  const qffHashId = cookieStore.get(QFF_HASH_ID_COOKIE_NAME)?.value ?? '';

  return (
    <SendPageViewEvent
      user={{
        isLoggedIn: !!qffSession?.user,
        userId: qffSession?.user.memberId ? qffHashId : '', // keep it same as qffHashId to align with loyalty and Q.com ecosystem
        qffHashId,
        qffPoints: qffSession?.user.pointBalance,
      }}
    />
  );
};
