import { sendGTMEvent } from '@next/third-parties/google';

import {
  ComponentType,
  createTabClickEvent,
  createTabClickEventPayload,
} from '@qantasexperiences/analytics';

import { PAGE_TYPE } from '~/app/[brand]/[channel]/_constants/analytics';

export const sendSearchPanelTabClickEvent = (componentVariant: 'sort' | 'filter') => {
  sendGTMEvent(
    createTabClickEvent(
      createTabClickEventPayload({
        componentType: ComponentType.FILTER_SORT_MODAL,
        componentVariant,
        pageType: PAGE_TYPE.SEARCH,
      }),
    ),
  );
};
