/**
 * @jest-environment jsdom
 */

import { sendGTMEvent } from '@next/third-parties/google';

import { sendSearchPanelTabClickEvent } from './sendSearchPanelTabClickEvent';

jest.mock('@next/third-parties/google');

Object.defineProperty(window, 'location', {
  value: {
    href: 'https://localhost:8000/qantas/holidays/search/list',
  },
  writable: true,
});

describe('sendSearchPanelTabClickEvent', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('sends a valid modal close GTM event', () => {
    sendSearchPanelTabClickEvent('sort');

    expect(sendGTMEvent).toHaveBeenCalledWith({
      event: 'tab_click',
      event_data: {
        action: 'click',
        item_type: 'button',
        component_type: 'filter_sort_modal',
        component_variant: 'sort',
        page_type: 'search',
        url: 'https://localhost:8000/qantas/holidays/search/list',
      },
    });
  });
});
