import type { ReactNode } from 'react';

import {
  useHolidaysSearchFilterState,
  useHolidaysSearchSortState,
  useSearchPanelState,
} from '@qantasexperiences/ui';
import { SrAnnouncementsProvider } from '@qantasexperiences/ui/providers';
import { render, screen, userEvent, waitFor } from '@qantasexperiences/ui/test-utils';

import { useFilterMeta, usePropertiesPagination } from '../../_hooks';
import { SearchPanelUiStateProvider } from '../../_providers';
import { StickySearchPanel } from './StickySearchPanel';

jest.mock('./hooks');
jest.mock('../../_hooks');
jest.mock('@qantasexperiences/ui', () => {
  const originalModule =
    jest.requireActual<typeof import('@qantasexperiences/ui')>('@qantasexperiences/ui');
  return {
    ...originalModule,
    useSearchPanelState: jest.fn(),
    useHolidaysSearchFilterState: jest.fn(),
    useHolidaysSearchSortState: jest.fn(),
  };
});
jest.mock('@qantasexperiences/data/client', () => ({
  useBlackoutDates: jest.fn().mockReturnValue({
    blackoutDates: {
      return: {},
      departure: {},
    },
  }),
}));

const DEFAULT_SEARCH_PANEL_STATE = {
  adults: 1,
  children: 0,
  infants: 0,
  departureDate: '2030-01-01',
  returnDate: '2030-01-08',
  originCode: 'BNE',
  destination: 'sydney',
};

const DEFAULT_FILTER_STATE = {
  minPrice: null,
  maxPrice: null,
  propertyRatings: null,
};

const DEFAULT_FILTER_HOOK_RETURN = {
  searchFilterState: DEFAULT_FILTER_STATE,
  setSearchFilterState: jest.fn(),
  clearFilterState: jest.fn(),
  isAnyFilterApplied: false,
};

const DEFAULT_SORT_STATE = null;

const BNE = {
  name: 'Brisbane',
  code: 'BNE',
  alias: null,
  type: 'Major City',
  displayName: 'Brisbane - BNE',
} as const;

const SYDNEY = {
  name: 'Sydney',
  slug: 'sydney',
  searchTags: null,
  airport: {
    name: 'Sydney Kingsford Smith',
    code: 'SYD',
    alias: 'Sydney',
    displayName: 'Sydney - SYD',
  },
  originCodes: ['ADL', 'PER', 'DRW', 'HBA', 'AVV', 'BNE', 'OOL'],
  state: 'New South Wales',
  country: 'Australia',
};

const MELBOURNE = {
  name: 'Melbourne',
  slug: 'melbourne',
  searchTags: null,
  airport: {
    name: 'Melbourne',
    code: 'MEL',
    alias: 'Melbourne',
    displayName: 'Melbourne - MEL',
  },
  originCodes: ['ADL', 'BNE', 'OOL'],
  state: 'Victoria',
  country: 'Australia',
};

const wrapper = ({ children }: { children: ReactNode }) => (
  <SrAnnouncementsProvider>
    <SearchPanelUiStateProvider>{children}</SearchPanelUiStateProvider>
  </SrAnnouncementsProvider>
);

describe('StickySearchPanel', () => {
  beforeEach(() => {
    jest.mocked(useSearchPanelState).mockReturnValue([DEFAULT_SEARCH_PANEL_STATE, jest.fn()]);
    jest.mocked(useHolidaysSearchFilterState).mockReturnValue(DEFAULT_FILTER_HOOK_RETURN);
    jest.mocked(useHolidaysSearchSortState).mockReturnValue([DEFAULT_SORT_STATE, jest.fn()]);
    jest
      .mocked(usePropertiesPagination)
      .mockReturnValue({ page: 2, setPage: jest.fn(), getPageHref: jest.fn() });
    jest.mocked(useFilterMeta).mockReturnValue({
      filterMeta: undefined,
      isFilterMetaLoading: false,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('has a section heading', () => {
    render(<StickySearchPanel destinations={[SYDNEY]} origins={[BNE]} />, {
      brand: 'qantas',
      channel: 'holidays',
      wrapper,
    });
    expect(screen.getByRole('heading', { level: 2, name: 'Search filters' })).toBeInTheDocument();
  });

  it("shows user's field selections in the search panel", () => {
    jest.mocked(useSearchPanelState).mockReturnValue([
      {
        adults: 1,
        children: 0,
        infants: 0,
        departureDate: '2030-01-01',
        returnDate: '2030-01-08',
        originCode: 'BNE',
        destination: 'sydney',
      },
      jest.fn(),
    ]);
    render(<StickySearchPanel destinations={[SYDNEY]} origins={[BNE]} />, {
      brand: 'qantas',
      channel: 'holidays',
      wrapper,
    });

    expect(screen.getByLabelText('Flying from')).toHaveValue('Brisbane - BNE');
    expect(screen.getByLabelText('Travelling to')).toHaveValue('Sydney');
    expect(screen.getByRole('button', { name: /When 1 Jan - 8 Jan Tue/ })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Travellers 1 traveller/ })).toBeInTheDocument();
  });

  it('updates search panel state when there is a change', async () => {
    const user = userEvent.setup();
    const mockSetSearchPanelState = jest.fn();
    const mockSetPage = jest.fn();

    jest
      .mocked(useSearchPanelState)
      .mockReturnValue([DEFAULT_SEARCH_PANEL_STATE, mockSetSearchPanelState]);
    jest
      .mocked(usePropertiesPagination)
      .mockReturnValue({ page: 3, setPage: mockSetPage, getPageHref: jest.fn() });

    render(<StickySearchPanel destinations={[SYDNEY, MELBOURNE]} origins={[BNE]} />, {
      brand: 'qantas',
      channel: 'holidays',
      wrapper,
    });

    const destinationInput = screen.getByLabelText('Travelling to');

    await user.clear(destinationInput);
    await user.type(destinationInput, 'Melbourne');
    await user.click(screen.getByRole('option', { name: /Melbourne/ }));

    expect(mockSetSearchPanelState).toHaveBeenCalledWith({ destination: 'melbourne' });
  });

  it('resets price range filter and pagination page to 1 when a new search is performed', async () => {
    const user = userEvent.setup();
    const mockSetSearchPanelState = jest.fn();
    const mockSetPage = jest.fn();
    const mockSetFilterState = jest.fn();

    jest
      .mocked(useSearchPanelState)
      .mockReturnValue([DEFAULT_SEARCH_PANEL_STATE, mockSetSearchPanelState]);
    jest
      .mocked(usePropertiesPagination)
      .mockReturnValue({ page: 3, setPage: mockSetPage, getPageHref: jest.fn() });
    jest.mocked(useHolidaysSearchFilterState).mockReturnValue({
      ...DEFAULT_FILTER_HOOK_RETURN,
      setSearchFilterState: mockSetFilterState,
    });

    render(<StickySearchPanel destinations={[SYDNEY, MELBOURNE]} origins={[BNE]} />, {
      brand: 'qantas',
      channel: 'holidays',
      wrapper,
    });

    const destinationInput = screen.getByLabelText('Travelling to');

    await user.clear(destinationInput);
    await user.type(destinationInput, 'Melbourne');
    await user.click(screen.getByRole('option', { name: /Melbourne/ }));

    expect(mockSetPage).toHaveBeenCalledWith(1);
    expect(mockSetFilterState).toHaveBeenCalledWith({ minPrice: null, maxPrice: null });
  });

  it('does NOT reset pagination page when a required field is missing', async () => {
    const user = userEvent.setup();

    const mockSetSearchPanelState = jest.fn();
    const mockSetPage = jest.fn();
    const mockSetFilterState = jest.fn();

    jest
      .mocked(useSearchPanelState)
      .mockReturnValue([DEFAULT_SEARCH_PANEL_STATE, mockSetSearchPanelState]);
    jest
      .mocked(usePropertiesPagination)
      .mockReturnValue({ page: 3, setPage: mockSetPage, getPageHref: jest.fn() });
    jest.mocked(useHolidaysSearchFilterState).mockReturnValue({
      ...DEFAULT_FILTER_HOOK_RETURN,
      setSearchFilterState: mockSetFilterState,
    });

    render(<StickySearchPanel destinations={[SYDNEY, MELBOURNE]} origins={[BNE]} />, {
      brand: 'qantas',
      channel: 'holidays',
      wrapper,
    });

    await user.clear(screen.getByLabelText('Flying from'));

    expect(mockSetPage).not.toHaveBeenCalled();
    expect(mockSetFilterState).not.toHaveBeenCalled();
  });

  it('resets pagination page when a filter is changed', async () => {
    const user = userEvent.setup();
    const mockSetPage = jest.fn();

    jest
      .mocked(usePropertiesPagination)
      .mockReturnValue({ page: 3, setPage: mockSetPage, getPageHref: jest.fn() });

    render(<StickySearchPanel destinations={[SYDNEY, MELBOURNE]} origins={[BNE]} />, {
      brand: 'qantas',
      channel: 'holidays',
      wrapper,
    });

    // FIXME:
    // This looks wrong, but Jest+RTL isn't reading Tailwind CSS, so the
    // display: none (which should hide elements from RTL) used to switch between
    // media sizes isn't being picked up.
    // We grab the first one, because we just want to open the modal.
    const filtersButton = screen.getAllByRole('button', { name: 'Filters' });
    await user.click(filtersButton[0]!);

    await waitFor(() => {
      expect(screen.getByText('Filter & Sort')).toBeInTheDocument();
    });

    await user.click(screen.getByLabelText('5 Stars'));
    await user.click(screen.getByRole('button', { name: 'Show packages' }));

    expect(mockSetPage).toHaveBeenCalledTimes(1);
    expect(mockSetPage).toHaveBeenCalledWith(1);
  });

  it('does not reset pagination page when a filter is not changed', async () => {
    const user = userEvent.setup();
    const mockSetPage = jest.fn();

    jest
      .mocked(usePropertiesPagination)
      .mockReturnValue({ page: 3, setPage: mockSetPage, getPageHref: jest.fn() });

    render(<StickySearchPanel destinations={[SYDNEY, MELBOURNE]} origins={[BNE]} />, {
      brand: 'qantas',
      channel: 'holidays',
      wrapper,
    });

    // FIXME:
    // This looks wrong, but Jest+RTL isn't reading Tailwind CSS, so the
    // display: none (which should hide elements from RTL) used to switch between
    // media sizes isn't being picked up.
    // We grab the first one, because we just want to open the modal.
    const filtersButton = screen.getAllByRole('button', { name: 'Filters' });
    await user.click(filtersButton[0]!);

    await waitFor(() => {
      expect(screen.getByText('Filter & Sort')).toBeInTheDocument();
    });

    await user.click(screen.getByRole('button', { name: 'Show packages' }));

    expect(mockSetPage).not.toHaveBeenCalled();
  });

  it('resets pagination page when the sort order is changed', async () => {
    const user = userEvent.setup();
    const mockSetPage = jest.fn();

    jest
      .mocked(usePropertiesPagination)
      .mockReturnValue({ page: 3, setPage: mockSetPage, getPageHref: jest.fn() });

    render(<StickySearchPanel destinations={[SYDNEY, MELBOURNE]} origins={[BNE]} />, {
      brand: 'qantas',
      channel: 'holidays',
      wrapper,
    });

    // FIXME:
    // This looks wrong, but Jest+RTL isn't reading Tailwind CSS, so the
    // display: none (which should hide elements from RTL) used to switch between
    // media sizes isn't being picked up.
    // We grab the first one, because we just want to open the modal.
    const sortButton = screen.getAllByRole('button', { name: 'Sort' });
    await user.click(sortButton[0]!);

    await waitFor(() => {
      expect(screen.getByText('Filter & Sort')).toBeInTheDocument();
    });

    await user.click(screen.getByLabelText('Price (lowest first)'));
    await user.click(screen.getByRole('button', { name: 'Apply' }));

    expect(mockSetPage).toHaveBeenCalledTimes(1);
    expect(mockSetPage).toHaveBeenCalledWith(1);
  });

  it('does not reset pagination page when the sort order is not changed', async () => {
    const user = userEvent.setup();
    const mockSetPage = jest.fn();

    jest
      .mocked(usePropertiesPagination)
      .mockReturnValue({ page: 3, setPage: mockSetPage, getPageHref: jest.fn() });

    render(<StickySearchPanel destinations={[SYDNEY, MELBOURNE]} origins={[BNE]} />, {
      brand: 'qantas',
      channel: 'holidays',
      wrapper,
    });

    // FIXME:
    // This looks wrong, but Jest+RTL isn't reading Tailwind CSS, so the
    // display: none (which should hide elements from RTL) used to switch between
    // media sizes isn't being picked up.
    // We grab the first one, because we just want to open the modal.
    const sortButton = screen.getAllByRole('button', { name: 'Sort' });
    await user.click(sortButton[0]!);

    await waitFor(() => {
      expect(screen.getByText('Filter & Sort')).toBeInTheDocument();
    });

    await user.click(screen.getByRole('button', { name: 'Apply' }));

    expect(mockSetPage).not.toHaveBeenCalled();
  });
});
