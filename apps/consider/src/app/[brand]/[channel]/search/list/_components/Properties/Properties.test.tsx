import type { CustomRenderOptions } from '@qantasexperiences/ui/test-utils';
import { SrAnnouncementsProvider } from '@qantasexperiences/ui/providers';
import { render, screen } from '@qantasexperiences/ui/test-utils';

import type { PropertiesProps } from './Properties';
import { PROPERTIES_PER_PAGE } from '../../_constants';
import { usePropertiesSearch, useSearchPanelUiState } from '../../_providers';
import { campaign as destinationCampaign, hilton, oakwood } from './_mocks';
import { Properties } from './Properties';

jest.mock('../../_providers');

jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn().mockReturnValue(new URLSearchParams()),
  usePathname: jest.fn().mockReturnValue('/test/route'),
}));

const renderProperties = (props?: Partial<PropertiesProps>, options?: CustomRenderOptions) =>
  render(<Properties origins={[]} campaigns={[]} {...props} />, {
    brand: 'qantas',
    channel: 'hotels',
    wrapper: ({ children }) => <SrAnnouncementsProvider>{children}</SrAnnouncementsProvider>,
    ...options,
  });

const DEFAULT_DATA = {
  promoCodes: [],
  propertiesWithOffer: [hilton, oakwood],
  meta: {
    searchOptions: {
      adults: 2,
      children: 0,
      departureDate: '2030-01-01',
      destinationCode: 'SYD',
      destinationName: 'sydney',
      infants: 0,
      originCode: 'DRW',
      returnDate: '2030-01-08',
      limit: 20,
      page: 1,
    },
    totalPackageOffers: 10,
  },
};

const DEFAULT_SEARCH_STATE = {
  adults: null,
  children: null,
  departureDate: null,
  destination: null,
  infants: null,
  originCode: null,
  returnDate: null,
};

const DEFAULT_SEARCH_UI_STATE = {
  uiState: {
    isDesktopTravelDatesOpen: false,
    isMobileSearchPanelOpen: false,
    isMobileTravelDatesOpen: false,
    isMobileTravellingToOpen: false,
    onDesktopTravelDatesOpenChange: jest.fn(),
    onMobileSearchPanelOpenChange: jest.fn(),
    onMobileTravelDatesOpenChange: jest.fn(),
    onMobileTravellingToOpenChange: jest.fn(),
    travellingToDesktopInputRef: { current: null },
  },
  openDesktopTravellingTo: jest.fn(),
  openDesktopTravelDates: jest.fn(),
  openMobileTravellingTo: jest.fn(),
  openMobileTravelDates: jest.fn(),
};

describe('Properties', () => {
  beforeEach(() => {
    jest.mocked(useSearchPanelUiState).mockReturnValue(DEFAULT_SEARCH_UI_STATE);
    jest.mocked(usePropertiesSearch).mockReturnValue({
      isLoading: false,
      data: DEFAULT_DATA,
      searchPanelStateForCurrentResponse: DEFAULT_SEARCH_STATE,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('has a section heading', () => {
    renderProperties();
    expect(screen.getByRole('heading', { level: 2, name: 'Search results' })).toBeInTheDocument();
  });

  it('shows the total number of properties', () => {
    jest.mocked(usePropertiesSearch).mockReturnValue({
      isLoading: false,
      data: {
        ...DEFAULT_DATA,
        meta: { ...DEFAULT_DATA.meta, totalPackageOffers: 50 },
      },
      searchPanelStateForCurrentResponse: DEFAULT_SEARCH_STATE,
    });
    renderProperties();

    expect(screen.getByText('50 packages available')).toBeVisible();

    expect(screen.getByRole('status')).toHaveTextContent('50 packages found');
  });

  it('shows the list of properties if there are properties', () => {
    renderProperties();

    expect(screen.getByRole('heading', { level: 3, name: 'Hilton Hotel' })).toBeInTheDocument();
    expect(screen.getByRole('heading', { level: 3, name: 'Oakwood Hotel' })).toBeInTheDocument();
  });

  it('shows no results section if there are no properties', () => {
    jest.mocked(usePropertiesSearch).mockReturnValue({
      isLoading: false,
      searchPanelStateForCurrentResponse: DEFAULT_SEARCH_STATE,
      data: {
        ...DEFAULT_DATA,
        promoCodes: [],
        propertiesWithOffer: [],
        meta: { ...DEFAULT_DATA.meta, totalPackageOffers: 0 },
      },
    });
    renderProperties();

    expect(screen.getByTestId('not-found-section')).toBeInTheDocument();
    expect(screen.getByRole('heading', { level: 3, name: 'No exact matches' })).toBeInTheDocument();

    expect(screen.getByRole('status')).toHaveTextContent('No exact matches found');
  });

  it('shows loading state if isLoading is true', () => {
    jest.mocked(usePropertiesSearch).mockReturnValue({
      isLoading: true,
      searchPanelStateForCurrentResponse: DEFAULT_SEARCH_STATE,
      data: DEFAULT_DATA,
    });
    renderProperties();

    expect(screen.getByText('Your dream holiday is on its way...')).toBeVisible();

    expect(screen.getByRole('status')).toHaveTextContent('Loading Search results');
  });

  it(`shows pagination nav if there is more than ${PROPERTIES_PER_PAGE} properties`, () => {
    jest.mocked(usePropertiesSearch).mockReturnValue({
      isLoading: false,
      searchPanelStateForCurrentResponse: DEFAULT_SEARCH_STATE,
      data: {
        ...DEFAULT_DATA,
        meta: { ...DEFAULT_DATA.meta, totalPackageOffers: PROPERTIES_PER_PAGE + 1 },
      },
    });

    renderProperties();

    expect(screen.getByRole('navigation', { name: 'pagination' })).toBeInTheDocument();
  });

  it(`does not show pagination nav if there is less than ${PROPERTIES_PER_PAGE} properties`, () => {
    jest.mocked(usePropertiesSearch).mockReturnValue({
      isLoading: false,
      searchPanelStateForCurrentResponse: DEFAULT_SEARCH_STATE,
      data: {
        ...DEFAULT_DATA,
        meta: { ...DEFAULT_DATA.meta, totalPackageOffers: PROPERTIES_PER_PAGE - 1 },
      },
    });

    renderProperties();

    expect(screen.queryByRole('navigation', { name: 'pagination' })).not.toBeInTheDocument();
  });

  it('shows no campaign banner when no matching campaign', () => {
    renderProperties({ campaigns: [] });

    expect(screen.queryByText(destinationCampaign.banner.text)).not.toBeInTheDocument();
  });

  it('shows campaign banner when there is a matching campaign', () => {
    renderProperties({ campaigns: [destinationCampaign] });

    expect(screen.getByRole('heading', { name: 'Active campaign' })).toBeInTheDocument();

    expect(screen.getByRole('status')).toHaveTextContent('Active campaign available');
  });
});
