'use client';

import type { HolidaysPostSearchPanelProps, SearchPanelStateValues } from '@qantasexperiences/ui';
import {
  HolidaysPostSearchPanel,
  useHolidaysSearchFilterState,
  useHolidaysSearchSortState,
  useSearchPanelState,
} from '@qantasexperiences/ui';

import {
  sendFilterApplyEvent,
  sendFilterOpenEvent,
  sendMapOpenClickEvent,
  sendSearchExitFieldEvent,
  sendSearchPanelCtaClickEvent,
  sendSearchPanelModalCloseEvent,
  sendSearchPanelTabClickEvent,
  sendSearchSubmitEvent,
  sendSortApplyEvent,
  sendSortOpenEvent,
  sendSortSelectEvent,
} from '../../_analytics';
import { useFilterMeta, usePropertiesPagination } from '../../_hooks';
import { useSearchPanelUiState } from '../../_providers';
import { useMapHref, useQffDetails } from './hooks';
import { checkHasStateChange } from './utils';

export type StickySearchPanelProps = Omit<
  HolidaysPostSearchPanelProps,
  | 'values'
  | 'setValues'
  | 'mapHref'
  | 'filterValues'
  | 'onFilterValuesChange'
  | 'filterFieldsMeta'
  | 'isFilterMetaLoading'
  | 'sortValue'
  | 'onSortValueChange'
  | 'analyticsEvents'
>;

const hasStateChanged = (
  changingStateValues: Partial<SearchPanelStateValues>,
  currentState: SearchPanelStateValues,
) => {
  return Object.entries(changingStateValues).some(([key, value]) => {
    // Object.entries defines all keys as 'string', this type assertion assigns it
    // back to its original value.
    return currentState[key as keyof typeof changingStateValues] !== value;
  });
};

const checkNewSearchPanelStateIsComplete = (
  changingStateValues: Partial<SearchPanelStateValues>,
  currentState: SearchPanelStateValues,
) => {
  const newState = {
    ...currentState,
    ...changingStateValues,
  };

  return Object.values(newState).every((stateValue) => stateValue !== null);
};

export const StickySearchPanel = (props: StickySearchPanelProps) => {
  const mapHref = useMapHref();
  const [searchPanelState, setSearchPanelState] = useSearchPanelState();
  const { searchFilterState, setSearchFilterState } = useHolidaysSearchFilterState();
  const [sortState, setSortState] = useHolidaysSearchSortState();
  const { filterMeta, isFilterMetaLoading } = useFilterMeta();
  const { page, setPage } = usePropertiesPagination();
  const qffProps = useQffDetails();
  const { uiState } = useSearchPanelUiState();

  const resetPage = () => {
    if (page !== 1) setPage(1);
  };

  const handleChangeState = (newState: Partial<SearchPanelStateValues>) => {
    if (!hasStateChanged(newState, searchPanelState)) {
      return;
    }

    if (checkNewSearchPanelStateIsComplete(newState, searchPanelState)) {
      resetPage();
      setSearchFilterState({ minPrice: null, maxPrice: null });
    }

    setSearchPanelState(newState);
  };

  const handleFilterValueChange: HolidaysPostSearchPanelProps['onFilterValuesChange'] = (
    newValue,
  ) => {
    if (checkHasStateChange(searchFilterState, newValue)) {
      setSearchFilterState(newValue);
      resetPage();
    }
  };

  const handleSortValueChange: HolidaysPostSearchPanelProps['onSortValueChange'] = (newValue) => {
    if (sortState !== newValue) {
      setSortState(newValue);
      resetPage();
    }
  };

  return (
    <div className="shadow-floating sticky top-0 z-10 w-full bg-white pt-6 pb-1 lg:pb-6">
      <section role="search" className="container">
        <h2 className="sr-only">Search filters</h2>
        <HolidaysPostSearchPanel
          {...props}
          values={searchPanelState}
          setValues={handleChangeState}
          qffProps={qffProps}
          mapHref={mapHref}
          filterValues={searchFilterState}
          onFilterValuesChange={handleFilterValueChange}
          filterFieldsMeta={filterMeta}
          isFilterMetaLoading={isFilterMetaLoading}
          sortValue={sortState}
          onSortValueChange={handleSortValueChange}
          analyticsEvents={{
            sendSortOpenEvent,
            sendSortSelectEvent,
            sendSortApplyEvent: (newSortValue) => sendSortApplyEvent(newSortValue, sortState),
            sendFilterOpenEvent,
            sendFilterApplyEvent: (newFilterValues) =>
              sendFilterApplyEvent(newFilterValues, searchFilterState, filterMeta),
            sendSearchExitFieldEvent,
            sendMapOpenClickEvent,
            sendSearchSubmitEvent,
            sendCtaClickEvent: sendSearchPanelCtaClickEvent,
            sendModalCloseEvent: sendSearchPanelModalCloseEvent,
            sendTabClickEvent: sendSearchPanelTabClickEvent,
          }}
          uiState={uiState}
        />
      </section>
    </div>
  );
};
