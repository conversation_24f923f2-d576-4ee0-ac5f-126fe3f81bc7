'use client';

import type { ReactNode } from 'react';

import { AuthProvider as BaseAuthProvider } from '@qantasexperiences/auth/client';
import { QF_VALUE_COOKIE_NAME } from '@qantasexperiences/auth/constants';
import { useClientEnvs } from '@qantasexperiences/env/client/react';
import { getCookie } from '@qantasexperiences/utils/browser';

import { sendLogoutEvent } from '../../_analytics';
import { removeBasePath } from './utils';

export interface AuthProviderProps {
  children?: ReactNode;
}

const onLogoutSuccess = () => {
  const userQffHash = getCookie(QF_VALUE_COOKIE_NAME) ?? '';
  sendLogoutEvent(userQffHash);
};

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const {
    NEXT_PUBLIC_QFF_CLIENT_ID,
    NEXT_PUBLIC_QFF_LOGIN_LINK,
    NEXT_PUBLIC_QFF_LOGOFF_LINK,
    NEXT_PUBLIC_QH_AUTH_API_BASE,
    NEXT_PUBLIC_BASE_PATH,
  } = useClientEnvs();

  return (
    <BaseAuthProvider
      clientId={NEXT_PUBLIC_QFF_CLIENT_ID}
      login={{
        ssoUrl: NEXT_PUBLIC_QFF_LOGIN_LINK,
        getRedirectPath: (currentPathname: string): string =>
          removeBasePath(currentPathname, NEXT_PUBLIC_BASE_PATH),
      }}
      logout={{
        ssoUrl: NEXT_PUBLIC_QFF_LOGOFF_LINK,
        onLogoutSuccess,
      }}
      qhAPIBase={NEXT_PUBLIC_QH_AUTH_API_BASE}
    >
      {children}
    </BaseAuthProvider>
  );
};
