import { AuthProvider as BaseAuthProvider } from '@qantasexperiences/auth/client';
import { useClientEnvs } from '@qantasexperiences/env/client/react';
import { render, screen } from '@qantasexperiences/ui/test-utils';

import { AuthProvider } from './AuthProvider';

jest.mock('@qantasexperiences/auth/client', () => ({
  AuthProvider: jest.fn(({ children }: { children: React.ReactNode }) => children),
}));

jest.mock('../../_analytics', () => ({
  sendLogoutEvent: jest.fn(),
}));

const NEXT_PUBLIC_QFF_CLIENT_ID = 'test-client-id';
const NEXT_PUBLIC_QFF_LOGIN_LINK = 'https://login.com';
const NEXT_PUBLIC_QFF_LOGOFF_LINK = 'https://logoff.com';
const NEXT_PUBLIC_QH_AUTH_API_BASE = 'https://auth-api.com';

jest.mock('@qantasexperiences/env/client/react');
const mockUseClientEnvs = jest.mocked(useClientEnvs);

describe('AuthProvider', () => {
  beforeEach(() => {
    mockUseClientEnvs.mockReturnValue({
      NEXT_PUBLIC_QFF_CLIENT_ID,
      NEXT_PUBLIC_QFF_LOGIN_LINK,
      NEXT_PUBLIC_QFF_LOGOFF_LINK,
      NEXT_PUBLIC_QH_AUTH_API_BASE,
      NEXT_PUBLIC_BASE_PATH: '/base',
    } as ReturnType<typeof useClientEnvs>);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it.each([
    {
      brand: 'qantas',
      channel: 'hotels',
    },
    {
      brand: 'qantas',
      channel: 'holidays',
    },
    {
      brand: 'jetstar',
      channel: 'hotels',
    },
    {
      brand: 'jetstar',
      channel: 'holidays',
    },
  ] as const)(
    'renders children and passes correct props to BaseAuthProvider for $brand $channel',
    ({ brand, channel }) => {
      render(<AuthProvider>Test Child</AuthProvider>, { brand, channel });

      expect(screen.getByText('Test Child')).toBeInTheDocument();

      expect(mockUseClientEnvs).toHaveBeenCalled();

      expect(BaseAuthProvider).toHaveBeenCalledWith(
        {
          clientId: NEXT_PUBLIC_QFF_CLIENT_ID,
          login: {
            ssoUrl: NEXT_PUBLIC_QFF_LOGIN_LINK,
            getRedirectPath: expect.any(Function) as (pathname: string) => string,
          },
          logout: {
            ssoUrl: NEXT_PUBLIC_QFF_LOGOFF_LINK,
            onLogoutSuccess: expect.any(Function) as () => void,
          },
          qhAPIBase: NEXT_PUBLIC_QH_AUTH_API_BASE,
          children: 'Test Child',
        },
        undefined,
      );
    },
  );
});
