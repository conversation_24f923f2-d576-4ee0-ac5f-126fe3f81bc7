import { sendGTMEvent } from '@next/third-parties/google';

import { createLoginEvent, createLoginEventPayload } from '@qantasexperiences/analytics';

import { PAGE_TYPE } from '~/app/[brand]/[channel]/_constants/analytics';

export const sendLoginEvent = ({
  userPoints,
  userQffHash,
}: {
  userPoints: number;
  userQffHash: string;
}) => {
  sendGTMEvent(
    createLoginEvent(
      createLoginEventPayload({
        isLoggedIn: true,
        userId: userQffHash, // keep it same as qffHash to align with the loyalty and qantas.com ecosystem
        userPoints,
        userQffHash,
        pageType: PAGE_TYPE.SEARCH,
      }),
    ),
  );
};
