import { sendGTMEvent } from '@next/third-parties/google';

import { sendLogoutEvent } from './sendLogoutEvent';

jest.mock('@next/third-parties/google');

describe('sendLogoutEvent', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('sends a logout click event with the correct event and event data values', () => {
    sendLogoutEvent('abcde');

    expect(sendGTMEvent).toHaveBeenCalledWith({
      event: 'logout',
      event_data: {
        action: 'logout',
        page_type: 'search',
      },
      user: {
        user_id: 'abcde',
        user_login_status: 'logged out',
        user_qff_hash: 'abcde',
      },
    });
  });
});
