import type { Locator, TestInfo } from '@playwright/test';
import { expect, test } from '@playwright/test';

import { JQ_CVP, QF_CVP } from '../acceptance/constants/search.constants';
import { searchResult, searchResultBali } from '../mocks';
import { verifyCVPItems, verifyFilter, verifySort } from './utils';

function getBaseUrl(testInfo: TestInfo): string {
  const isCI = process.env.CI === 'true';
  const brand = testInfo.title.includes('QF') ? 'Qantas' : 'Jetstar';
  if (isCI) {
    if (brand === 'Qantas') {
      return 'https://sit-qp-customer-ui.qantashotels.com';
    }
    return 'https://sit-jetstar-holidays-ui.qantashotels.com/au/en';
  } else if (brand === 'Qantas') {
    return 'http://localhost:3000/consider/qantas';
  }
  return 'http://localhost:3000/consider/jetstar';
}

test.describe('Search1b page Acceptance Tests', () => {
  let newBaseURL: string;
  test.beforeEach(({}, testInfo) => {
    newBaseURL = getBaseUrl(testInfo);
  });

  test('QF Desktop - Default state of Source/Destination', async ({ page }) => {
    const fromTooltip: Locator = page.getByTestId('search-error-tooltip');
    const flyingFrom: Locator = page.getByPlaceholder('City or airport');
    const travellingTo: Locator = page.getByPlaceholder('Destination');
    const calendar: Locator = page.getByTestId('date-input-xl');

    const notFoundSection: Locator = page.getByTestId('not-found-section');
    const callUsLink: Locator = notFoundSection.getByRole('link', {
      name: 'Call us on 1 3 7 0 6 6 for assistance',
    });
    const requestCallbackLink: Locator = notFoundSection.getByRole('link', {
      name: 'Request a callback',
    });

    await page.goto(`${newBaseURL}/holidays/search/list`);
    await expect(page).toHaveTitle(/qantas holidays/i);
    await expect(fromTooltip).toBeVisible();
    await expect(flyingFrom).toBeEnabled();
    await expect(travellingTo).toBeDisabled();
    await expect(calendar).toBeDisabled();
    await expect(callUsLink).toBeVisible();
    await expect(requestCallbackLink).toBeVisible();
  });

  test('QF Mobile - Default state of Source/Destination', async ({ page }) => {
    const fromTooltip: Locator = page.getByTestId('search-input-error');
    const flyingFrom: Locator = page.getByPlaceholder('City or airport');
    const travellingTo: Locator = page.getByPlaceholder('Destination');
    const calendar: Locator = page.getByTestId('date-input-xl');
    const searchPanel: Locator = page.getByTestId('holidaysCompactSearchPanel');

    const notFoundSection: Locator = page.getByTestId('not-found-section');
    const callUsLink: Locator = notFoundSection.getByRole('link', {
      name: 'Call us on 1 3 7 0 6 6 for assistance',
    });
    const requestCallbackLink: Locator = notFoundSection.getByRole('link', {
      name: 'Request a callback',
    });

    await page.setViewportSize({
      width: 430,
      height: 932,
    });

    await page.goto(`${newBaseURL}/holidays/search/list`);
    await expect(callUsLink).toBeVisible();
    await expect(requestCallbackLink).toBeVisible();
    await searchPanel.click();
    await expect(fromTooltip).toBeVisible();
    await expect(flyingFrom).toBeEnabled();
    await expect(travellingTo).toBeDisabled();
    await expect(calendar).toBeDisabled();
  });

  test('QF Desktop - Execute Search Function - Mocked', async ({ page }) => {
    const flyingFrom: Locator = page.getByPlaceholder('City or airport');
    const travellingTo: Locator = page.getByPlaceholder('Destination');
    const from: Locator = page.locator('div[data-key="ADL"]');
    const to: Locator = page.locator('div[data-key="canberra"]');
    const calendar: Locator = page.getByTestId('date-input-xl');
    const calHeader: Locator = page.getByTestId('header-title');
    const depDate: Locator = page.locator(
      '[aria-label*="First available date"][aria-hidden="false"]',
    );
    const availableDate: Locator = page.locator(
      'div[role="button"]:not([aria-disabled="true"]):not([aria-hidden="true"])',
    );
    const confirmSearch: Locator = page.getByRole('button', { name: 'Confirm' });
    const propertyName = page.locator('.text-2xl.font-bold');

    await page.route('**/get-holidays-properties-with-offer**', async (route) => {
      const json = searchResult;
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(json),
      });
    });

    await page.goto(`${newBaseURL}/holidays/search/list`);
    await flyingFrom.click();
    await page.keyboard.type('adelaide', { delay: 100 });
    const fromCity = await from.textContent();
    await from.click();
    expect(await flyingFrom.getAttribute('value')).toStrictEqual(fromCity);

    await travellingTo.click();
    await page.keyboard.type('canberra', { delay: 100 });
    const toCity = await to.textContent();
    await to.click();
    expect(await travellingTo.getAttribute('value')).toStrictEqual(toCity);

    await calendar.waitFor();
    await calendar.click();
    await expect(calHeader).toHaveText('Select departure date');
    await depDate.waitFor();
    await depDate.click();
    await expect(calHeader).toHaveText('Select return date');
    const departureDateLabel = await depDate.getAttribute('aria-label');

    const elements = await availableDate.all();
    const labels = await Promise.all(elements.map((el) => el.getAttribute('aria-label')));
    const dateToClick = elements.findIndex((_, i) => labels[i] !== departureDateLabel);
    if (dateToClick !== -1) {
      await elements[dateToClick]?.click();
    }

    await confirmSearch.click();
    await expect(calendar).not.toContainText('Select travel dates');
    const expectedPropertyName = searchResult.propertiesWithOffer[0]?.name;
    await expect(propertyName.filter({ hasText: expectedPropertyName })).toBeVisible();
  });

  test('QF Mobile - Execute Search Function - Mocked', async ({ page }) => {
    const searchPanel: Locator = page.getByTestId('holidaysCompactSearchPanel');
    const flyingFrom: Locator = page.locator(
      '[data-testid="search-input-value"] >> text=City or airport',
    );
    const flyingAirport: Locator = page.getByTestId('searchInputField');
    const travellingTo: Locator = page.locator(
      '[data-testid="search-input-value"] >> text=Destination',
    );
    const from: Locator = page.locator('div[data-key="ADL"]');
    const to: Locator = page.locator('div[data-key="canberra"]');
    const searchFrom: Locator = page.locator(
      '[data-testid="search-input-value"] >> text=Adelaide - ADL',
    );
    const searchTo: Locator = page.locator('[data-testid="search-input-value"] >> text=Canberra');
    const calInput: Locator = page.locator(
      '[data-testid="search-input-value"] >> text=Select travel dates',
    );
    const calHeader: Locator = page.getByTestId('header-title');
    const depDate: Locator = page.locator(
      '[aria-label*="First available date"][aria-hidden="false"]',
    );
    const availableDate: Locator = page.locator(
      'div[role="button"]:not([aria-disabled="true"]):not([aria-hidden="true"])',
    );
    const confirmDate: Locator = page.getByRole('button', { name: 'Confirm' });
    const confirmSearch: Locator = page.getByText('Search', { exact: true });
    const propertyName = page.locator('.text-2xl.font-bold');

    await page.route('**/get-holidays-properties-with-offer**', async (route) => {
      const json = searchResult;
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(json),
      });
    });

    await page.setViewportSize({
      width: 430,
      height: 932,
    });

    await page.goto(`${newBaseURL}/holidays/search/list`);
    await searchPanel.click();
    await flyingFrom.click();
    await flyingAirport.click();
    await flyingAirport.fill('adelaide');
    const fromCity = await from.textContent();
    await from.click();
    expect(await searchFrom.textContent()).toStrictEqual(fromCity); // Verify the correct city gets updated in the input box

    await travellingTo.click();
    await flyingAirport.click();
    await flyingAirport.fill('canberra');
    const toCity = await to.textContent();
    await to.click();
    expect(await searchTo.textContent()).toStrictEqual(toCity);

    // Calendar Entry
    await calInput.click();
    await expect(calHeader).toHaveText('Select departure date');
    await depDate.click();
    await expect(calHeader).toHaveText('Select return date');
    const departureDateLabel = await depDate.getAttribute('aria-label');
    const elements = await availableDate.all();
    const labels = await Promise.all(elements.map((el) => el.getAttribute('aria-label')));
    const dateToClick = elements.findIndex((_, i) => labels[i] !== departureDateLabel);
    if (dateToClick !== -1) {
      await elements[dateToClick]?.click();
    }
    await confirmDate.click();
    await expect(calInput).toBeHidden();
    await confirmSearch.click();
    const expectedPropertyName = searchResult.propertiesWithOffer[0]?.name;
    await expect(propertyName.filter({ hasText: expectedPropertyName })).toBeVisible();
  });

  test('JQ Desktop - Default state of Source/Destination', async ({ page }) => {
    const fromTooltip: Locator = page.getByTestId('search-error-tooltip');
    const flyingFrom: Locator = page.getByPlaceholder('City or airport');
    const travellingTo: Locator = page.getByPlaceholder('Destination');
    const calendar: Locator = page.getByTestId('date-input-xl');

    await page.goto(`${newBaseURL}/holidays/search/list`, { waitUntil: 'load' });
    await expect(page).toHaveTitle(/jetstar holidays/i);
    await expect(fromTooltip).toBeVisible();
    await expect(flyingFrom).toBeEnabled();
    await expect(travellingTo).toBeDisabled();
    await expect(calendar).toBeDisabled();
  });

  test('JQ Mobile - Default state of Source/Destination', async ({ page }) => {
    const fromTooltip: Locator = page.getByTestId('search-input-error');
    const flyingFrom: Locator = page.getByPlaceholder('City or airport');
    const travellingTo: Locator = page.getByPlaceholder('Destination');
    const calendar: Locator = page.getByTestId('date-input-xl');
    const searchPanel: Locator = page.getByTestId('holidaysCompactSearchPanel');

    await page.setViewportSize({
      width: 430,
      height: 932,
    });

    await page.goto(`${newBaseURL}/holidays/search/list`);
    await searchPanel.click();
    await expect(fromTooltip).toBeVisible();
    await expect(flyingFrom).toBeEnabled();
    await expect(travellingTo).toBeDisabled();
    await expect(calendar).toBeDisabled();
  });

  test('JQ Desktop - Execute Search Function - Mocked', async ({ page }) => {
    const flyingFrom: Locator = page.getByPlaceholder('City or airport');
    const travellingTo: Locator = page.getByPlaceholder('Destination');
    const from: Locator = page.locator('div[data-key="BNE"]');
    const to: Locator = page.locator('div[data-key="canberra"]');
    const calendar: Locator = page.getByTestId('date-input-xl');
    const calHeader: Locator = page.getByTestId('header-title');
    const depDate: Locator = page.locator(
      '[aria-label*="First available date"][aria-hidden="false"]',
    );
    const availableDate: Locator = page.locator(
      'div[role="button"]:not([aria-disabled="true"]):not([aria-hidden="true"])',
    );
    const confirmSearch: Locator = page.getByRole('button', { name: 'Confirm' });
    const propertyName = page.locator('.text-2xl.font-bold');

    await page.route('**/get-holidays-properties-with-offer**', async (route) => {
      const json = searchResult;
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(json),
      });
    });

    await page.goto(`${newBaseURL}/holidays/search/list`);
    await flyingFrom.click();
    await page.keyboard.type('brisbane', { delay: 100 });
    const fromCity = await from.textContent();
    await from.click();
    expect(await flyingFrom.getAttribute('value')).toStrictEqual(fromCity);

    await travellingTo.waitFor({ state: 'visible' });
    await travellingTo.click();
    await page.keyboard.type('canberra', { delay: 100 });
    const toCity = await to.textContent();
    await to.click();
    expect(await travellingTo.getAttribute('value')).toStrictEqual(toCity);

    await calendar.click();
    await expect(calHeader).toHaveText('Select departure date');
    await depDate.click();
    await expect(calHeader).toHaveText('Select return date');
    const departureDateLabel = await depDate.getAttribute('aria-label');
    const elements = await availableDate.all();
    const labels = await Promise.all(elements.map((el) => el.getAttribute('aria-label')));
    const dateToClick = elements.findIndex((_, i) => labels[i] !== departureDateLabel);
    if (dateToClick !== -1) {
      await elements[dateToClick]?.click();
    }
    await confirmSearch.click();
    await expect(calendar).not.toContainText('Select travel dates');
    const expectedPropertyName = searchResult.propertiesWithOffer[0]?.name;
    await expect(propertyName.filter({ hasText: expectedPropertyName })).toBeVisible();
  });

  test('JQ Mobile - Execute Search Function - Mocked', async ({ page }) => {
    const searchPanel: Locator = page.getByTestId('holidaysCompactSearchPanel');
    const flyingFrom: Locator = page.locator(
      '[data-testid="search-input-value"] >> text=City or airport',
    );
    const flyingAirport: Locator = page.getByTestId('searchInputField');
    const travellingTo: Locator = page.locator(
      '[data-testid="search-input-value"] >> text=Destination',
    );
    const from: Locator = page.locator('div[data-key="BNE"]');
    const to: Locator = page.locator('div[data-key="canberra"]');
    const searchFrom: Locator = page.locator(
      '[data-testid="search-input-value"] >> text=Brisbane - BNE',
    );
    const searchTo: Locator = page.locator('[data-testid="search-input-value"] >> text=Canberra');
    const calInput: Locator = page.locator(
      '[data-testid="search-input-value"] >> text=Select travel dates',
    );
    const calHeader: Locator = page.getByTestId('header-title');
    const depDate: Locator = page.locator(
      '[aria-label*="First available date"][aria-hidden="false"]',
    );
    const availableDate: Locator = page.locator(
      'div[role="button"]:not([aria-disabled="true"]):not([aria-hidden="true"])',
    );
    const confirmDate: Locator = page.getByRole('button', { name: 'Confirm' });
    const confirmSearch: Locator = page.getByText('Search', { exact: true });
    const propertyName = page.locator('.text-2xl.font-bold');

    await page.route('**/get-holidays-properties-with-offer**', async (route) => {
      const json = searchResult;
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(json),
      });
    });

    await page.setViewportSize({
      width: 430,
      height: 932,
    });

    await page.goto(`${newBaseURL}/holidays/search/list`);
    await searchPanel.click();
    await flyingFrom.click();
    await flyingAirport.click();
    await page.keyboard.type('brisbane', { delay: 100 });
    const fromCity = await from.textContent();
    await from.click();
    expect(await searchFrom.textContent()).toStrictEqual(fromCity); // Verify the correct city gets updated in the input box

    await travellingTo.click();
    await flyingAirport.click();
    await page.keyboard.type('canberra', { delay: 100 });
    const toCity = await to.textContent();
    await to.click();
    expect(await searchTo.textContent()).toStrictEqual(toCity);

    // Calendar Entry
    await calInput.click();
    await expect(calHeader).toHaveText('Select departure date');
    await depDate.click();
    await expect(calHeader).toHaveText('Select return date');
    const departureDateLabel = await depDate.getAttribute('aria-label');
    const elements = await availableDate.all();
    const labels = await Promise.all(elements.map((el) => el.getAttribute('aria-label')));
    const dateToClick = elements.findIndex((_, i) => labels[i] !== departureDateLabel);
    if (dateToClick !== -1) {
      await elements[dateToClick]?.click();
    }
    await confirmDate.click();
    await expect(calInput).toBeHidden();
    await confirmSearch.click();
    const expectedPropertyName = searchResult.propertiesWithOffer[0]?.name;
    await expect(propertyName.filter({ hasText: expectedPropertyName })).toBeVisible();
  });

  test('QF Desktop - Cash/Points toggles - Mocked', async ({ page }) => {
    const payCash = page.getByTestId('toggle-group-container').locator('input[value="cash"]');
    const payPoints = page.getByTestId('toggle-group-container').locator('input[value="points"]');
    const payToggle: Locator = page.locator(
      '[data-testid="toggle-group-container"] label >> text=points',
    );
    // const pricePointsLocator = page.locator('p[data-testid="price-points"] span');
    // const propertyCard: Locator = page
    //   .getByTestId('property-card')
    //   .filter({ hasText: 'Novotel Canberra' });

    await page.route('**/get-holidays-properties-with-offer**', async (route) => {
      const json = searchResult;
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(json),
      });
    });

    await page.goto(
      `${newBaseURL}/holidays/search/list?originCode=ADL&destination=canberra&adults=2&children=0&departureDate=2026-07-20&infants=0&returnDate=2026-07-25`,
      { waitUntil: 'load' },
    );
    // Verify default toggle state
    await expect(payCash).toHaveAttribute('tabindex', '0');
    await expect(payPoints).toHaveAttribute('tabindex', '-1');

    // Change toggle to point
    await payToggle.click();
    await expect(payCash).toHaveAttribute('tabindex', '-1');
    await expect(payPoints).toHaveAttribute('tabindex', '0');
    // Verify the points displayed -
    // const allPoints = await propertyCard.locator(pricePointsLocator).allTextContents();
    // const totalPoints = allPoints.slice(0, 3).join('');
    // TO be enabled after points campaign testing is reverted back
    // const expectedText = '161,414'; // As this is mocked response, the expected points are fixed
    // expect(totalPoints).toBe(expectedText);
  });

  test('QF Mobile - Cash/Points toggles - Mocked', async ({ page }) => {
    const payCash = page.getByTestId('toggle-group-container').locator('input[value="cash"]');
    const payPoints = page.getByTestId('toggle-group-container').locator('input[value="points"]');
    const payToggle: Locator = page.locator(
      '[data-testid="toggle-group-container"] label >> text=points',
    );
    // const pricePointsLocator = page.locator('p[data-testid="price-points"] span');
    // const propertyCard: Locator = page
    //   .getByTestId('property-card')
    //   .filter({ hasText: 'Novotel Canberra' });

    await page.route('**/get-holidays-properties-with-offer**', async (route) => {
      const json = searchResult;
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(json),
      });
    });

    await page.setViewportSize({
      width: 430,
      height: 932,
    });

    await page.goto(
      `${newBaseURL}/holidays/search/list?originCode=ADL&destination=canberra&adults=2&children=0&departureDate=2026-07-20&infants=0&returnDate=2026-07-25`,
    );

    // Verify default toggle state
    await expect(payCash).toHaveAttribute('tabindex', '0');
    await expect(payPoints).toHaveAttribute('tabindex', '-1');

    // Change toggle to point
    await payToggle.click();
    await expect(payCash).toHaveAttribute('tabindex', '-1');
    await expect(payPoints).toHaveAttribute('tabindex', '0');
    // Verify the points displayed -
    // const allPoints = await propertyCard.locator(pricePointsLocator).allTextContents();
    // const totalPoints = allPoints.slice(0, 3).join('');
    // TO be enabled after points campaign testing is reverted back
    // const expectedText = '161,414'; // As this is mocked response, the expected points are fixed
    // expect(totalPoints).toBe(expectedText);
  });

  test('QF Desktop - Lightbox & Package Details - Mocked', async ({ page }) => {
    function getLocator(parent: Locator, testId: string) {
      return parent.getByTestId(testId);
    }

    const propertyCard: Locator = page
      .getByTestId('property-card')
      .filter({ hasText: 'W Bali - Seminyak' });
    const propertyMedia: Locator = page.getByLabel('W Bali - Seminyak');
    const openGallery = propertyCard.locator(
      'button[aria-label="Open image in fullscreen"][tabindex="0"]',
    );
    const closeDialog = page.getByTestId('icon-close');
    const propertyEyebrow = 'property-eyebrow';
    const propertyRating = propertyCard.locator('.text-icon-rating');
    const packageInclusions = propertyCard.locator(`text="Package inclusions"`);
    const cashPrice = 'price-cash';
    const navRight = 'icon-chevron-small-right';
    const navLeft = 'icon-chevron-small-left';
    // const packageDetails = propertyCard.locator(`text="View package details and terms"`);
    // const packageModal = page.getByTestId('modal');
    // const inclusions = packageModal.locator('span.font-normal');

    await page.route('**/get-holidays-properties-with-offer**', async (route) => {
      const json = searchResultBali;
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(json),
      });
    });

    await page.goto(
      `${newBaseURL}/holidays/search/list?originCode=SYD&destination=bali&adults=2&children=0&departureDate=2026-06-15&infants=0&returnDate=2026-06-17`,
      { waitUntil: 'load' },
    );
    const eyebrow = getLocator(propertyCard, propertyEyebrow);
    await eyebrow.isVisible();
    await propertyRating.isVisible();
    await packageInclusions.isVisible();
    const price = getLocator(propertyCard, cashPrice);
    await expect(price).toBeVisible();
    // Navigate through Media Gallery
    await getLocator(propertyCard, navRight).click();
    await getLocator(propertyCard, navRight).click();
    await getLocator(propertyCard, navLeft).click();
    await getLocator(propertyCard, navLeft).click();
    await openGallery.click();
    await getLocator(propertyMedia, navRight).click();
    await getLocator(propertyMedia, navRight).click();
    await getLocator(propertyMedia, navLeft).click();
    await getLocator(propertyMedia, navLeft).click();
    await page.getByRole('option').and(page.getByLabel('Image 3 of')).click();
    await page.getByRole('option').and(page.getByLabel('Image 5 of')).click();
    await closeDialog.click();

    // TO be enabled if PO offers are present in SIT
    // await packageDetails.click();
    // const elements = await inclusions.all();
    // const inclusionTexts = await Promise.all(elements.map((el) => el.textContent()));
    // inclusionTexts.forEach((inclusion, i) => {
    //   const expInclusion =
    //     searchResultBali.propertiesWithOffer[0]?.procuredOffer?.inclusions[i]?.title;
    //   expect(inclusion?.trim()).toStrictEqual(expInclusion?.trim());
    // });
    // await closeDialog.click();
  });

  test('QF Mobile - Lightbox & Package Details - Mocked', async ({ page }) => {
    function getLocator(parent: Locator, testId: string) {
      return parent.getByTestId(testId);
    }

    const propertyCard: Locator = page
      .getByTestId('property-card')
      .filter({ hasText: 'W Bali - Seminyak' });
    const propertyMedia: Locator = page.getByLabel('W Bali - Seminyak');
    const openGallery = propertyCard.locator(
      'button[aria-label="Open image in fullscreen"][tabindex="0"]',
    );
    const closeDialog = page.getByTestId('icon-close');
    const propertyEyebrow = 'property-eyebrow';
    const propertyRating = propertyCard.locator('.text-icon-rating');
    const packageInclusions = propertyCard.locator(`text="Package inclusions"`);
    const cashPrice = 'price-cash';
    const navRight = 'icon-chevron-small-right';
    const navLeft = 'icon-chevron-small-left';
    // const packageDetails = propertyCard.locator(`text="View package details and terms"`);
    // const packageModal = page.getByTestId('modal');
    // const inclusions = packageModal.locator('span.font-normal');

    await page.route('**/get-holidays-properties-with-offer**', async (route) => {
      const json = searchResultBali;
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(json),
      });
    });

    await page.setViewportSize({
      width: 430,
      height: 932,
    });

    await page.goto(
      `${newBaseURL}/holidays/search/list?originCode=SYD&destination=bali&adults=2&children=0&departureDate=2026-06-15&infants=0&returnDate=2026-06-17`,
      { waitUntil: 'load' },
    );
    const eyebrow = getLocator(propertyCard, propertyEyebrow);
    await eyebrow.isVisible();
    await propertyRating.isVisible();
    await packageInclusions.isVisible();
    const price = getLocator(propertyCard, cashPrice);
    await expect(price).toBeVisible();
    // Navigate through Media Gallery
    await getLocator(propertyCard, navRight).click();
    await getLocator(propertyCard, navRight).click();
    await getLocator(propertyCard, navLeft).click();
    await getLocator(propertyCard, navLeft).click();
    await openGallery.click();
    await getLocator(propertyMedia, navRight).click();
    await getLocator(propertyMedia, navRight).click();
    await getLocator(propertyMedia, navLeft).click();
    await getLocator(propertyMedia, navLeft).click();
    await page.getByRole('option').and(page.getByLabel('Image 3 of')).click();
    await page.getByRole('option').and(page.getByLabel('Image 5 of')).click();
    await closeDialog.click();

    // TO be enabled if PO offers are present in SIT
    // await packageDetails.click();
    // const elements = await inclusions.all();
    // const inclusionTexts = await Promise.all(elements.map((el) => el.textContent()));
    // inclusionTexts.forEach((inclusion, i) => {
    //   const expInclusion =
    //     searchResultBali.propertiesWithOffer[0]?.procuredOffer?.inclusions[i]?.title;
    //   expect(inclusion?.trim()).toStrictEqual(expInclusion?.trim());
    // });
    // await closeDialog.click();
  });

  test('JQ Desktop - Lightbox & Package Details - Mocked', async ({ page }) => {
    function getLocator(parent: Locator, testId: string) {
      return parent.getByTestId(testId);
    }

    const propertyCard: Locator = page
      .getByTestId('property-card')
      .filter({ hasText: 'W Bali - Seminyak' });
    const propertyMedia: Locator = page.getByLabel('W Bali - Seminyak');
    const openGallery = propertyCard.locator(
      'button[aria-label="Open image in fullscreen"][tabindex="0"]',
    );
    const closeDialog = page.getByTestId('icon-close');
    const propertyEyebrow = 'property-eyebrow';
    const propertyRating = propertyCard.locator('.text-icon-rating');
    const packageInclusions = propertyCard.locator(`text="Package inclusions"`);
    const cashPrice = 'price-cash';
    const navRight = 'icon-chevron-small-right';
    const navLeft = 'icon-chevron-small-left';
    // const packageDetails = propertyCard.locator(`text="View package details and terms"`);
    // const packageModal = page.getByTestId('modal');
    // const inclusions = packageModal.locator('span.font-normal');

    await page.route('**/get-holidays-properties-with-offer**', async (route) => {
      const json = searchResultBali;
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(json),
      });
    });

    await page.goto(
      `${newBaseURL}/holidays/search/list?originCode=SYD&destination=bali&adults=2&children=0&departureDate=2026-06-15&infants=0&returnDate=2026-06-17`,
      { waitUntil: 'load' },
    );
    const eyebrow = getLocator(propertyCard, propertyEyebrow);
    await eyebrow.isVisible();
    await propertyRating.isVisible();
    await packageInclusions.isVisible();
    const price = getLocator(propertyCard, cashPrice);
    await expect(price).toBeVisible();
    // Navigate through Media Gallery\
    await getLocator(propertyCard, navRight).click();
    await getLocator(propertyCard, navRight).click();
    await getLocator(propertyCard, navLeft).click();
    await getLocator(propertyCard, navLeft).click();
    await openGallery.click();
    await getLocator(propertyMedia, navRight).click();
    await getLocator(propertyMedia, navRight).click();
    await getLocator(propertyMedia, navLeft).click();
    await getLocator(propertyMedia, navLeft).click();
    await page.getByRole('option').and(page.getByLabel('Image 3 of')).click();
    await page.getByRole('option').and(page.getByLabel('Image 5 of')).click();
    await closeDialog.click();

    // TO be enabled if PO offers are present in SIT
    // await packageDetails.click();
    // const elements = await inclusions.all();
    // const inclusionTexts = await Promise.all(elements.map((el) => el.textContent()));
    // inclusionTexts.slice(0, -1).forEach((inclusion, i) => {
    //   const expInclusion =
    //     searchResultBali.propertiesWithOffer[0]?.procuredOffer?.inclusions[i]?.title;
    //   expect(inclusion?.trim()).toStrictEqual(expInclusion?.trim());
    // });
    // await closeDialog.click();
  });

  test('JQ Mobile - Lightbox & Package Details - Mocked', async ({ page }) => {
    function getLocator(parent: Locator, testId: string) {
      return parent.getByTestId(testId);
    }

    const propertyCard: Locator = page
      .getByTestId('property-card')
      .filter({ hasText: 'W Bali - Seminyak' });
    const propertyMedia: Locator = page.getByLabel('W Bali - Seminyak');
    const openGallery = propertyCard.locator(
      'button[aria-label="Open image in fullscreen"][tabindex="0"]',
    );
    const closeDialog = page.getByTestId('icon-close');
    const propertyEyebrow = 'property-eyebrow';
    const propertyRating = propertyCard.locator('.text-icon-rating');
    const packageInclusions = propertyCard.locator(`text="Package inclusions"`);
    const cashPrice = 'price-cash';
    const navRight = 'icon-chevron-small-right';
    const navLeft = 'icon-chevron-small-left';
    // const packageDetails = propertyCard.locator(`text="View package details and terms"`);
    // const packageModal = page.getByTestId('modal');
    // const inclusions = packageModal.locator('span.font-normal');

    await page.route('**/get-holidays-properties-with-offer**', async (route) => {
      const json = searchResultBali;
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(json),
      });
    });

    await page.setViewportSize({
      width: 430,
      height: 932,
    });

    await page.goto(
      `${newBaseURL}/holidays/search/list?originCode=SYD&destination=bali&adults=2&children=0&departureDate=2026-06-15&infants=0&returnDate=2026-06-17`,
      { waitUntil: 'load' },
    );
    const eyebrow = getLocator(propertyCard, propertyEyebrow);
    await eyebrow.isVisible();
    await propertyRating.isVisible();
    await packageInclusions.isVisible();
    const price = getLocator(propertyCard, cashPrice);
    await expect(price).toBeVisible();
    // Navigate through Media Gallery
    await getLocator(propertyCard, navRight).click();
    await getLocator(propertyCard, navRight).click();
    await getLocator(propertyCard, navLeft).click();
    await getLocator(propertyCard, navLeft).click();
    await openGallery.click();
    await getLocator(propertyMedia, navRight).click();
    await getLocator(propertyMedia, navRight).click();
    await getLocator(propertyMedia, navLeft).click();
    await getLocator(propertyMedia, navLeft).click();
    await page.getByRole('option').and(page.getByLabel('Image 3 of')).click();
    await page.getByRole('option').and(page.getByLabel('Image 5 of')).click();
    await closeDialog.click();

    // TO be enabled if PO offers are present in SIT

    // await packageDetails.click();
    // const elements = await inclusions.all();
    // const inclusionTexts = await Promise.all(elements.map((el) => el.textContent()));
    // inclusionTexts.slice(0, -1).forEach((inclusion, i) => {
    //   const expInclusion =
    //     searchResultBali.propertiesWithOffer[0]?.procuredOffer?.inclusions[i]?.title;
    //   expect(inclusion?.trim()).toStrictEqual(expInclusion?.trim());
    // });
    // await closeDialog.click();
  });
});

test.describe('CVP Acceptance Tests', () => {
  const mobileViewport = { width: 430, height: 932 };

  const testCases = [
    { brand: 'QF', data: QF_CVP, isJetstar: false },
    { brand: 'JQ', data: JQ_CVP, isJetstar: true },
  ];

  const viewports = [
    { name: 'Desktop', config: null },
    { name: 'Mobile', config: mobileViewport },
  ];

  for (const { brand, data, isJetstar } of testCases) {
    for (const { name, config } of viewports) {
      test(`${brand} ${name} - CVP`, async ({ page }, testInfo) => {
        const baseURL = getBaseUrl(testInfo);
        if (config) {
          await page.setViewportSize(config);
        }
        await page.goto(`${baseURL}/holidays/search/list`);
        await verifyCVPItems(page, data, isJetstar);
        expect(page).not.toBeNull(); // Dummy assertion to satisfy the linter: playwright/expect-expect
      });
    }
  }
});

test.describe('Filter, Sort & Map modal Acceptance Tests', () => {
  const mobileViewport = { width: 430, height: 932 };
  const testCases = [{ brand: 'QF' }, { brand: 'JQ' }];

  const viewports = [
    { name: 'Desktop', config: null },
    { name: 'Mobile', config: mobileViewport },
  ];

  for (const { brand } of testCases) {
    for (const { name, config } of viewports) {
      test(`${brand} ${name} - Filter`, async ({ page }, testInfo) => {
        await page.route('**/get-holidays-properties-with-offer**', async (route) => {
          const json = searchResultBali;
          await route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify(json),
          });
        });
        const baseURL = getBaseUrl(testInfo);
        if (config) {
          await page.setViewportSize(config);
        }
        await page.goto(
          `${baseURL}/holidays/search/list?originCode=SYD&destination=bali&adults=2&children=0&departureDate=2026-06-20&infants=0&page=1&returnDate=2026-06-22`,
        );
        await verifyFilter(page);
        expect(page).not.toBeNull();
      });
    }
  }

  for (const { brand } of testCases) {
    for (const { name, config } of viewports) {
      test(`${brand} ${name} - Sort`, async ({ page }, testInfo) => {
        await page.route('**/get-holidays-properties-with-offer**', async (route) => {
          const json = searchResult;
          await route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify(json),
          });
        });
        const baseURL = getBaseUrl(testInfo);
        if (config) {
          await page.setViewportSize(config);
        }
        await page.goto(
          `${baseURL}/holidays/search/list?originCode=SYD&destination=canberra&adults=2&children=0&departureDate=2026-06-20&infants=0&page=1&returnDate=2026-06-22`,
        );
        await verifySort(page);
        expect(page).not.toBeNull();
      });
    }
  }

  for (const { brand } of testCases) {
    for (const { name, config } of viewports) {
      test(`${brand} ${name} - Map`, async ({ page }, testInfo) => {
        const map = page.getByRole('link', { name: 'Map' });
        await page.route('**/get-holidays-properties-with-offer**', async (route) => {
          const json = searchResult;
          await route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify(json),
          });
        });
        const baseURL = getBaseUrl(testInfo);
        if (config) {
          await page.setViewportSize(config);
        }
        await page.goto(
          `${baseURL}/holidays/search/list?originCode=BNE&destination=canberra&adults=2&children=0&departureDate=2026-06-20&infants=0&page=1&returnDate=2026-06-22`,
        );
        await map.click();
        await expect(page).toHaveURL(/canberra\/map\?/);
      });
    }
  }
});
