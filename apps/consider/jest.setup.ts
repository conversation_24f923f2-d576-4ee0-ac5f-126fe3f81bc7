import '@testing-library/jest-dom';

global.ResizeObserver = class MockedResizeObserver {
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();
};

// Only take all browser fully support properties from: https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API#browser_compatibility
global.IntersectionObserver = class MockedIntersectionObserver {
  root = null;
  rootMargin = '';
  thresholds = [];
  takeRecords = jest.fn();
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();
};

global.matchMedia = jest.fn();

jest.mock('../../packages/ui/src/providers/a11y/SrAnnouncementsProvider/SrAnnouncementsProvider');
