import { redirect } from 'next/navigation';

import { getSessionData } from '@qantasexperiences/auth/server';
import { render, screen, waitFor } from '@qantasexperiences/ui/test-utils';

import { APPLICATION_NAME } from './_constants';
import HomePage, { metadata } from './page';

const { env } = process;

jest.mock('next/navigation');
const mockedRedirect = jest.mocked(redirect);

jest.mock('@qantasexperiences/auth/server', () => ({
  getSessionData: jest.fn(),
}));
const mockGetSessionData = jest.mocked(getSessionData);

beforeEach(() => {
  process.env.BRAND = 'qantas';
  process.env.CHANNEL = 'hotels';
  process.env.PLATFORM = 'external';
  mockGetSessionData.mockResolvedValue(undefined);
});

afterEach(() => {
  jest.clearAllMocks();
  process.env = env;
});

test('returns metadata', () => {
  expect(metadata).toEqual({
    title: `Home | ${APPLICATION_NAME}`,
  });
});

test('redirects to login when platform is internal', async () => {
  process.env.PLATFORM = 'internal';

  const result = await HomePage({});
  render(result, { brand: process.env.BRAND, channel: process.env.CHANNEL });

  expect(mockedRedirect).toHaveBeenCalledWith('/login');
});

test('renders home page when platform is external', async () => {
  process.env.PLATFORM = 'external';

  const result = await HomePage({});
  render(result, { brand: process.env.BRAND, channel: process.env.CHANNEL });

  expect(screen.getByTestId('background-image')).toBeInTheDocument();
  expect(screen.getByRole('heading', { level: 1, name: 'Hotel Bookings' })).toBeInTheDocument();
});

test('redirects to booking list page when platform is external and user is authenticated', async () => {
  mockGetSessionData.mockResolvedValue({
    accessToken: 'QFF_ACCESS_TOKEN',
  } as Awaited<ReturnType<typeof getSessionData>>);

  const result = await HomePage({});
  render(result, { brand: process.env.BRAND, channel: process.env.CHANNEL });

  await waitFor(() => expect(mockedRedirect).toHaveBeenCalledWith('/bookings'));
});
