import { getFontClasses } from '@qantasexperiences/theming/fonts';
import { <PERSON><PERSON><PERSON>us<PERSON><PERSON><PERSON>, <PERSON>Footer, PageHeader } from '@qantasexperiences/ui';

import './global.css';

import type { Metadata } from 'next';
import type { FC, ReactNode } from 'react';
import { Suspense, useId } from 'react';
import { GoogleTagManager } from '@next/third-parties/google';

import { TenantProvider } from '@qantasexperiences/tenants/react';

import { APPLICATION_NAME, FOOTER_LINKS } from '~/app/_constants';
import clientEnv from '~/env/client';
import AuthProvider from './_components/AuthProvider';
import FooterClickEvent from './_components/FooterClickEvent';
import PageViewEvent from './_components/PageViewEvent';

interface Props {
  children: ReactNode;
}

export const metadata: Metadata = {
  title: {
    template: `%s | ${APPLICATION_NAME}`,
    default: `${APPLICATION_NAME}`,
  },
};

const RootLayout: FC<Props> = ({ children }) => {
  const { NEXT_PUBLIC_QANTAS_HOTELS_EXTERNAL_GTM_ID } = clientEnv;

  const tenant = { brand: process.env.BRAND, channel: process.env.CHANNEL };

  const footerContainerId = useId();

  return (
    <html lang="en" className={getFontClasses(process.env.BRAND)} data-theme={process.env.BRAND}>
      <body className="flex min-h-screen flex-col">
        <DevStatusBanner isEnabled={false} />
        <TenantProvider value={tenant}>
          <AuthProvider platform={process.env.PLATFORM}>
            <PageHeader tenant={tenant} hideUniversalNav hideQffRibbon hideContact />
            <main id="main-content" className="flex flex-1 basis-0 flex-col bg-white">
              {children}
            </main>
            <div id={footerContainerId}>
              <PageFooter
                tenant={tenant}
                links={FOOTER_LINKS[process.env.BRAND]?.[process.env.CHANNEL]}
              />
              <Suspense fallback={null}>
                <FooterClickEvent containerId={footerContainerId} />
              </Suspense>
            </div>
          </AuthProvider>
        </TenantProvider>
        {/* TODO: To be updated when other tenant/platform are supported */}
        {NEXT_PUBLIC_QANTAS_HOTELS_EXTERNAL_GTM_ID && (
          <>
            <GoogleTagManager gtmId={NEXT_PUBLIC_QANTAS_HOTELS_EXTERNAL_GTM_ID} />
            <PageViewEvent />
          </>
        )}
      </body>
    </html>
  );
};

export default RootLayout;
