import type { Metadata } from 'next';
import type { FC } from 'react';
import { redirect } from 'next/navigation';

import { BackgroundImage } from '@qantasexperiences/ui';

import islandBackground from './_assets/island-background.png';
import HomeCard from './_components/HomeCard';
import { APPLICATION_NAME } from './_constants';
import redirectIfAuthenticated from './_utils/redirectIfAuthenticated';

export const metadata: Metadata = {
  title: `Home | ${APPLICATION_NAME}`,
};

const HomePage: FC = async () => {
  if (process.env.PLATFORM === 'internal') {
    redirect('/login');
  }

  await redirectIfAuthenticated();

  return (
    <BackgroundImage data-testid="background-image" src={islandBackground} className="h-full">
      <div className="flex h-full items-center justify-center px-4 py-8 md:py-20">
        <HomeCard />
      </div>
    </BackgroundImage>
  );
};

export default HomePage;
