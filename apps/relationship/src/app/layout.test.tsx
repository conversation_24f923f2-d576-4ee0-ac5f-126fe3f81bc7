import type { FC, ReactNode } from 'react';
import { cookies } from 'next/headers';
import { GoogleTagManager, sendGTMEvent } from '@next/third-parties/google';
import { render, screen, waitFor, within } from '@testing-library/react';

import {
  ActionType,
  ComponentType,
  createFooterClickEvent,
  ItemType,
} from '@qantasexperiences/analytics';
import { getSessionData } from '@qantasexperiences/auth/server';
import { getFontClasses } from '@qantasexperiences/theming/fonts';
import { userEvent } from '@qantasexperiences/ui/test-utils';

import clientEnv from '~/env/client';
import PageViewEvent from './_components/PageViewEvent';
import { APPLICATION_NAME } from './_constants';
import RootLayout, { metadata } from './layout';

const { env } = process;

jest.mock('next/link', () => {
  const mockLink: FC<{ children: ReactNode; href: string }> = ({ children, href }) => (
    <a
      href={href}
      onClick={(e) => {
        e.preventDefault();
      }}
    >
      {children}
    </a>
  );
  mockLink.displayName = 'Link';
  return mockLink;
});

jest.mock('next/headers');
const get = jest.fn();
const mockCookies = jest.mocked(cookies);

jest.mock('@next/third-parties/google');
const MockGoogleTagManager = jest.mocked(GoogleTagManager);
const mockSendGTMEvent = jest.mocked(sendGTMEvent);

jest.mock('@qantasexperiences/auth/server', () => ({
  getSessionData: jest.fn(),
}));
const mockGetSessionData = jest.mocked(getSessionData);

jest.mock('./_components/PageViewEvent');
const MockPageViewEvent = jest.mocked(PageViewEvent);

const pathname = '/';
const params = {};
const searchParams = new URLSearchParams();
jest.mock('next/navigation', () => ({
  useParams: () => params,
  usePathname: () => pathname,
  useSearchParams: () => searchParams,
}));

const { NEXT_PUBLIC_QANTAS_HOTELS_EXTERNAL_GTM_ID } = clientEnv;

beforeEach(() => {
  process.env.BRAND = 'qantas';
  process.env.CHANNEL = 'hotels';
  process.env.PLATFORM = 'external';
  mockCookies.mockResolvedValue({ get } as unknown as Awaited<ReturnType<typeof cookies>>);
  mockGetSessionData.mockResolvedValue(undefined);
});

afterEach(() => {
  Object.defineProperty(clientEnv, 'NEXT_PUBLIC_QANTAS_HOTELS_EXTERNAL_GTM_ID', {
    value: NEXT_PUBLIC_QANTAS_HOTELS_EXTERNAL_GTM_ID,
    writable: true,
  });
  jest.clearAllMocks();
  process.env = env;
});

test('returns metadata', () => {
  expect(metadata).toEqual({
    title: {
      template: `%s | ${APPLICATION_NAME}`,
      default: APPLICATION_NAME,
    },
  });
});

test('renders root layout with children in main for skip to content link', () => {
  const mainId = 'main-content';
  render(<RootLayout>CHILDREN</RootLayout>, { container: document });

  expect(screen.getByRole('link', { name: 'Skip to content' })).toHaveAttribute(
    'href',
    `#${mainId}`,
  );

  const main = screen.getByRole('main');
  expect(main).toHaveAttribute('id', mainId);
  expect(within(main).getByText('CHILDREN')).toBeInTheDocument();
});

test.each(['qantas', 'jetstar'] as const)('renders root layout with %s styles', (brand) => {
  process.env.BRAND = brand;

  render(<RootLayout>CHILDREN</RootLayout>, { container: document });

  expect(document.documentElement).toHaveAttribute('data-theme', brand);
  expect(document.documentElement).toHaveClass(getFontClasses(brand));
});

test.each([
  { brand: 'qantas', channel: 'hotels', expectedPrefix: 'Qantas Hotels' },
  { brand: 'qantas', channel: 'holidays', expectedPrefix: 'Qantas Holidays' },
  { brand: 'jetstar', channel: 'hotels', expectedPrefix: 'Jetstar Hotels' },
  { brand: 'jetstar', channel: 'holidays', expectedPrefix: 'Jetstar Holidays' },
] as const)(
  'renders header with hidden sections and footer for $brand $channel',
  ({ brand, channel, expectedPrefix }) => {
    process.env.BRAND = brand;
    process.env.CHANNEL = channel;

    render(<RootLayout>CHILDREN</RootLayout>, { container: document });

    const header = screen.getByRole('banner');

    expect(
      within(header).getByRole('link', { name: `${expectedPrefix} home page` }),
    ).toBeInTheDocument();
    expect(
      // eslint-disable-next-line testing-library/no-node-access
      header.querySelector('[data-widget-type="global_features_navigation"]'),
    ).not.toBeInTheDocument();
    expect(within(header).queryByRole('link', { name: 'Log in' })).not.toBeInTheDocument();
    expect(within(header).getAllByRole('link')).toHaveLength(2);

    expect(
      screen.getByRole('contentinfo', { name: `${expectedPrefix} Footer` }),
    ).toBeInTheDocument();
  },
);

test('renders google tag manager and sends page view event when GTM_ID is available', async () => {
  const GTM_ID = 'GTM-XXXX';
  Object.defineProperty(clientEnv, 'NEXT_PUBLIC_QANTAS_HOTELS_EXTERNAL_GTM_ID', {
    value: GTM_ID,
    writable: true,
  });

  render(<RootLayout>CHILDREN</RootLayout>, { container: document });

  await waitFor(() => {
    expect(screen.getByText('CHILDREN')).toBeInTheDocument();
  });

  expect(MockGoogleTagManager).toHaveBeenCalledWith({ gtmId: GTM_ID }, undefined);
  expect(MockPageViewEvent).toHaveBeenCalled();
});

test('does not render google tag manager or send page view event when GTM_ID is not available', () => {
  render(<RootLayout>CHILDREN</RootLayout>, { container: document });

  expect(MockGoogleTagManager).not.toHaveBeenCalled();
  expect(mockSendGTMEvent).not.toHaveBeenCalled();
});

test('renders footer click event and sends event when footer link is clicked', async () => {
  const user = userEvent.setup();
  render(<RootLayout>CHILDREN</RootLayout>, { container: document });

  await waitFor(() => {
    expect(screen.getByText('CHILDREN')).toBeInTheDocument();
  });

  await user.click(screen.getByText('Terms of Use'));

  expect(mockSendGTMEvent).toHaveBeenCalledWith(
    expect.objectContaining(
      createFooterClickEvent({
        event_data: expect.objectContaining({
          action: ActionType.CLICK,
          component_type: ComponentType.FOOTER,
          group_name: 'footer',
          item_type: ItemType.LINK,
        }),
      }),
    ),
  );
});
