import type { Metadata } from 'next';
import type { FC } from 'react';
import { redirect } from 'next/navigation';
import { HTTPError } from 'ky';

import clientEnv from '~/env/client';
import RedirectToLegacySelfService from './_components/RedirectToLegacySelfService';
import { Filter } from './_enums';
import getBookings from './_utils/getBookings';
import isFilter from './_utils/isFilter';

interface Props {
  searchParams: Promise<{ filter?: string }>;
}

export const metadata: Metadata = {
  title: process.env.PLATFORM === 'external' ? 'My bookings' : 'Latest bookings',
};

const BookingsPage: FC<Props> = async ({ searchParams }) => {
  const { NEXT_PUBLIC_USE_LEGACY_SS_BOOKINGS_LIST_PAGE_ENABLED } = clientEnv;
  const searchParamsFilter = (await searchParams).filter;
  const filter: Filter = isFilter(searchParamsFilter) ? searchParamsFilter : Filter.UPCOMING;

  try {
    await getBookings(filter);

    if (process.env.PLATFORM === 'external') {
      if (NEXT_PUBLIC_USE_LEGACY_SS_BOOKINGS_LIST_PAGE_ENABLED) {
        return <RedirectToLegacySelfService path={`/bookings?filter=${filter}`} />;
      }

      return <h1 className="text-heading-1 font-bold">My bookings</h1>;
    }

    return <h1 className="text-heading-1 font-bold">Latest bookings</h1>;
  } catch (error) {
    if (error instanceof HTTPError && error.response.status === 401) {
      return redirect('/login');
    }

    throw error;
  }
};

export default BookingsPage;
