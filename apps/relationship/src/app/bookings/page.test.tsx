import type { NormalizedOptions } from 'ky';
import { headers } from 'next/headers';
import { redirect, useRouter } from 'next/navigation';
import { render, screen, waitFor } from '@testing-library/react';
import { HTTPError } from 'ky';

import { useLogin } from '@qantasexperiences/auth/client';

import clientEnv from '~/env/client';
import { Filter } from './_enums';
import getBookings from './_utils/getBookings';
import BookingsPage from './page';

const { env } = process;

jest.mock('@qantasexperiences/auth/client');

jest.mock('next/headers');
const mockedHeaders = jest.mocked(headers);

jest.mock('./_utils/getBookings');
const mockGetBookings = jest.mocked(getBookings);

jest.mock('@qantasexperiences/auth/server', () => ({
  getSessionData: jest.fn().mockResolvedValue(undefined),
}));

jest.mock('~/env/client');
const mockClientEnv = jest.mocked(clientEnv);

jest.mock('next/navigation');
const mockUseRouter = jest.mocked(useRouter);
const mockReplace = jest.fn();
const mockRedirect = jest.mocked(redirect);

beforeEach(() => {
  process.env.PLATFORM = 'external';
  Object.defineProperty(window, 'location', {
    value: { href: 'http://localhost:3000' },
    writable: true,
  });
  mockedHeaders.mockResolvedValue({ get: () => null } as unknown as Awaited<
    ReturnType<typeof headers>
  >);
  mockClientEnv.NEXT_PUBLIC_USE_LEGACY_SS_BOOKINGS_LIST_PAGE_ENABLED = false;
  mockUseRouter.mockReturnValue({ replace: mockReplace } as unknown as ReturnType<
    typeof useRouter
  >);
});

afterEach(() => {
  process.env = env;
  jest.clearAllMocks();
});

test('redirects to legacy self service booking list page when NEXT_PUBLIC_USE_LEGACY_SS_BOOKINGS_LIST_PAGE_ENABLED flag is true and platform is external', async () => {
  mockClientEnv.NEXT_PUBLIC_USE_LEGACY_SS_BOOKINGS_LIST_PAGE_ENABLED = true;

  const result = await BookingsPage({
    searchParams: Promise.resolve({ filter: Filter.UPCOMING }),
  });

  render(result);

  expect(mockReplace).toHaveBeenCalledWith(
    `${mockClientEnv.NEXT_PUBLIC_LEGACY_SELF_SERVICE_BASE}/bookings?filter=${Filter.UPCOMING}`,
  );

  expect(screen.getByText('Please wait...')).toBeInTheDocument();
});

test('does not redirect to legacy self service booking list page when NEXT_PUBLIC_USE_LEGACY_SS_BOOKINGS_LIST_PAGE_ENABLED flag is true and platform is internal', async () => {
  mockClientEnv.NEXT_PUBLIC_USE_LEGACY_SS_BOOKINGS_LIST_PAGE_ENABLED = true;

  process.env.PLATFORM = 'internal';

  const result = await BookingsPage({
    searchParams: Promise.resolve({ filter: Filter.UPCOMING }),
  });

  render(result);

  expect(mockReplace).not.toHaveBeenCalled();
  expect(screen.getByRole('heading', { level: 1, name: 'Latest bookings' })).toBeInTheDocument();
});

test('does not redirect to legacy self service booking list page when NEXT_PUBLIC_USE_LEGACY_SS_BOOKINGS_LIST_PAGE_ENABLED flag is false', async () => {
  mockClientEnv.NEXT_PUBLIC_USE_LEGACY_SS_BOOKINGS_LIST_PAGE_ENABLED = false;

  const result = await BookingsPage({
    searchParams: Promise.resolve({ filter: Filter.UPCOMING }),
  });

  render(result);

  expect(mockReplace).not.toHaveBeenCalled();
  expect(screen.getByRole('heading', { level: 1, name: 'My bookings' })).toBeInTheDocument();
});

test.each([
  { platform: 'external', expectedTitle: 'My bookings' },
  { platform: 'internal', expectedTitle: 'Latest bookings' },
] as const)('returns metadata for $platform platform', async ({ platform, expectedTitle }) => {
  process.env.PLATFORM = platform;
  jest.resetModules();

  const { metadata } = await import('./page');
  expect(metadata).toEqual({ title: expectedTitle });
});

test('renders heading when platform is external with filter', async () => {
  const result = await BookingsPage({
    searchParams: Promise.resolve({ filter: Filter.UPCOMING }),
  });

  render(result);

  expect(screen.getByRole('heading', { level: 1, name: 'My bookings' })).toBeInTheDocument();
});

test('renders heading when platform is external with no filter', async () => {
  const result = await BookingsPage({
    searchParams: Promise.resolve({}),
  });

  render(result);

  expect(screen.getByRole('heading', { level: 1, name: 'My bookings' })).toBeInTheDocument();
});

test('renders heading when platform is external with invalid filter', async () => {
  const result = await BookingsPage({
    searchParams: Promise.resolve({ filter: 'invalid' }),
  });

  render(result);

  expect(screen.getByRole('heading', { level: 1, name: 'My bookings' })).toBeInTheDocument();
});

test('renders heading when platform is internal', async () => {
  process.env.PLATFORM = 'internal';
  const result = await BookingsPage({
    searchParams: Promise.resolve({}),
  });

  render(result);

  expect(screen.getByRole('heading', { level: 1, name: 'Latest bookings' })).toBeInTheDocument();
});

test('throws error when an unexpected error occurs', async () => {
  const error = new Error('Unexpected error');
  mockGetBookings.mockRejectedValue(error);

  await expect(async () => {
    const result = await BookingsPage({
      searchParams: Promise.resolve({ filter: Filter.UPCOMING }),
    });
    render(result);
  }).rejects.toThrow('Unexpected error');

  expect(
    screen.queryByRole('heading', { level: 1, name: 'Latest bookings' }),
  ).not.toBeInTheDocument();
});

test('redirects to login page when unauthorized (401)', async () => {
  const mockLoginUrl = 'https://mock-login-url.com';
  jest.mocked(useLogin).mockReturnValue(mockLoginUrl);

  const mockError = new HTTPError(
    {
      status: 401,
      json: () => Promise.resolve({ required_authorization_method: 'qff_single_sign_on' }),
    } as Response,
    {} as Request,
    {} as NormalizedOptions,
  );
  mockGetBookings.mockRejectedValue(mockError);

  await BookingsPage({
    searchParams: Promise.resolve({ filter: Filter.UPCOMING }),
  });

  await waitFor(() => {
    expect(mockRedirect).toHaveBeenCalledWith('/login');
  });
});
