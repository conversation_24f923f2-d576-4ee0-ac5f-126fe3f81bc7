import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

import { AUTH_COOKIES } from '@qantasexperiences/auth/constants';

// eslint-disable-next-line no-restricted-imports -- To avoid https://nextjs.org/docs/messages/edge-dynamic-code-evaluation
import {
  RELATIONSHIP_ACCESS_TOKEN_COOKIE_NAME,
  RELATIONSHIP_REDIRECT_COOKIE_NAME,
} from '~/app/_constants/cookies';

const withRedirectPath = ({ cookies, nextUrl }: NextRequest): NextResponse => {
  const sbaAccessToken = cookies.get(RELATIONSHIP_ACCESS_TOKEN_COOKIE_NAME);
  const qffAuthSession = cookies.get(AUTH_COOKIES.QH_AUTH_SESSION);

  if (!sbaAccessToken && !qffAuthSession) {
    const response = NextResponse.next();
    response.cookies.set(RELATIONSHIP_REDIRECT_COOKIE_NAME, nextUrl.pathname);
    return response;
  }

  if ((sbaAccessToken || qffAuthSession) && cookies.get(RELATIONSHIP_REDIRECT_COOKIE_NAME)) {
    const response = NextResponse.next();
    response.cookies.delete(RELATIONSHIP_REDIRECT_COOKIE_NAME);
    return response;
  }

  return NextResponse.next();
};

export default withRedirectPath;
