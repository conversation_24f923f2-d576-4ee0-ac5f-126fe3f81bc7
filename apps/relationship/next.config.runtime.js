const requiredEnvs = ['BASE_PATH', 'ASSET_PREFIX'];
const hasRequiredRuntimeVars = requiredEnvs.every((env) => process.env[env]);

if (!hasRequiredRuntimeVars) {
  throw new Error(
    `Missing required environment variables:\n${requiredEnvs.map((env) => `- ${env}`).join('\n')}\n` +
      'These variables are crucial for determining the runtime configuration.',
  );
}

/** @type {import('next').NextConfig} */
export default {
  htmlLimitedBots: '.*', // https://github.com/vercel/next.js/issues/79313
  basePath: process.env.BASE_PATH,
  assetPrefix: process.env.ASSET_PREFIX,
  poweredByHeader: false,
};
