import { withSentryConfig } from '@sentry/nextjs';
import { createJiti } from 'jiti';

const jiti = createJiti(new URL(import.meta.url).pathname);

await jiti.import('./src/env/client');
const { serverEnv } = await jiti.import('./src/env/server');

const requiredEnvs = ['BRAND', 'CHANNEL', 'PLATFORM', 'BASE_PATH'];
const isDev = process.argv.some((arg) => arg.includes('dev'));
const isBuild = process.argv.some((arg) => arg.includes('build'));
const isValidTenant = requiredEnvs.every((env) => process.env[env]);

if ((isDev || isBuild) && !isValidTenant) {
  throw new Error(
    `Missing required environment variables:\n${requiredEnvs.map((env) => `- ${env}`).join('\n')}\n` +
      'These variables are crucial for determining the build configuration.',
  );
}

const distDir = isDev
  ? `.next/${process.env.BRAND}_${process.env.CHANNEL}_${process.env.PLATFORM}`
  : undefined;

/** @type {import('next').NextConfig} */
const defaults = {
  htmlLimitedBots: '.*', // https://github.com/vercel/next.js/issues/79313
  eslint: {
    ignoreDuringBuilds: true,
  },
  basePath: process.env.BASE_PATH,
  assetPrefix: serverEnv.ASSET_PREFIX,
  distDir,
  poweredByHeader: false,
  reactStrictMode: true,
  experimental: {
    optimizePackageImports: ['@qantasexperiences/ui'],
  },
  env: {
    BRAND: process.env.BRAND,
    CHANNEL: process.env.CHANNEL,
    PLATFORM: process.env.PLATFORM,
  },
  /** Enables hot reloading for local packages without a build step */
  transpilePackages: [
    '@qantasexperiences/env',
    '@qantasexperiences/jest',
    '@qantasexperiences/logger',
    '@qantasexperiences/sentry',
    '@qantasexperiences/theming',
    '@qantasexperiences/ui',
    '@qantasexperiences/utils',
    '@t3-oss/env-nextjs',
    '@t3-oss/env-core',
    'ky',
    // These 4 are all required to resolve issues with camelcase-keys in jest
    'camelcase-keys',
    'map-obj',
    'camelcase',
    'quick-lru',
    'uuid',
  ],
  /* https://react-svgr.com/docs/next/ */
  webpack: (config) => {
    // Grab the existing rule that handles SVG imports
    // @ts-expect-error https://react-svgr.com/docs/next/
    const fileLoaderRule = config.module.rules.find((rule) => rule.test?.test?.('.svg'));

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] }, // exclude if *.svg?url
        use: ['@svgr/webpack'],
      },
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
};

// Injected content via Sentry wizard below
export default withSentryConfig(defaults, {
  // For all available options, see:
  // https://github.com/getsentry/sentry-webpack-plugin#options

  // Suppresses source map uploading logs during build
  silent: true,
  org: 'qantashotels',
  project: 'experiences-relationship-app',

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Hides source maps from generated client bundles
  hideSourceMaps: true,

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,
});
