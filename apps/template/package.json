{"name": "@qantasexperiences/template-app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "clean": "rm -rf .turbo node_modules", "dev": "next dev", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint:css": "stylelint **/*.css --ignore-path ../../.gitignore --allow-empty-input", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "start": "next start", "typecheck": "tsc --noEmit", "test:unit": "jest", "test:unit:ci": "jest --ci --coverage", "test:unit:watch": "jest --watch", "test:acceptance": "playwright test --project=acceptance", "test:acceptance:ui": "playwright test --project=acceptance --ui", "test:e2e": "playwright test --project=e2e", "test:e2e:ui": "playwright test --project=e2e --ui", "test:high-level": "playwright test", "test:high-level:ui": "playwright test --ui", "test:high-level:debug": "playwright test --debug", "test:high-level:ci": "sh -c 'Xvfb :99 -screen 0 1280x720x24 > /dev/null 2>&1 & export DISPLAY=:99 && pnpm run test:high-level'", "test:pa11y": "pa11y-ci"}, "dependencies": {"@contentful/rich-text-react-renderer": "^16.1.0", "@contentful/rich-text-types": "^17.1.0", "@next/third-parties": "^15.4.4", "@qantasexperiences/contentful": "workspace:*", "@qantasexperiences/env": "workspace:*", "@qantasexperiences/logger": "workspace:*", "@qantasexperiences/sentry": "workspace:*", "@qantasexperiences/tenants": "workspace:*", "@qantasexperiences/theming": "workspace:*", "@qantasexperiences/ui": "workspace:*", "@qantasexperiences/utils": "workspace:*", "@sentry/nextjs": "^9.42.0", "@t3-oss/env-nextjs": "^0.13.8", "camelcase-keys": "^9.1.3", "next": "^15.4.4", "pluralize": "^8.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "zod": "^3.25.67"}, "devDependencies": {"@playwright/test": "1.54.1", "@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/jest": "workspace:*", "@qantasexperiences/playwright-config": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.4", "@types/jest": "^30.0.0", "@types/node": "^22.16.5", "@types/pluralize": "^0.0.33", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}, "prettier": "@qantasexperiences/code-style/prettier"}