#!/bin/bash
set -euo pipefail

echo "== Locate removeUtmParams definition =="
rg -n --hidden --no-ignore -A 3 -B 3 $'export (const|function) removeUtmParams|function removeUtmParams\\s*\\(' || true

echo
echo "== Find where removeUtmParams is re-exported from @qantasexperiences/utils/formatting =="
rg -n --hidden --no-ignore -A 2 -B 2 "removeUtmParams" packages || true

echo
echo "== Inspect Link component definition and href prop type =="
# Try common component locations; fall back to any Link.* under packages/ui
fd -a -HI --strip-cwd-prefix -t f 'Link.tsx' packages/ui || true
fd -a -HI --strip-cwd-prefix -t f 'Link.*' packages/ui | rg -v 'PropertyCard' || true

echo
echo "== Show likely Link props typing =="
rg -n --hidden --no-ignore -A 5 -B 5 $'interface\\s+LinkProps|type\\s+LinkProps|props:\\s*\\{' packages/ui | rg -A 5 -B 5 "href" || true

echo
echo "== Grep usages to infer expected types for href =="
rg -n --hidden --no-ignore "href={" packages/ui | head -n 50 || true