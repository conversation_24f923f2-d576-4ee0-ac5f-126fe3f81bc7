{"name": "@qantasexperiences/code-style", "description": "A consolidated package for managing all code style tools, including ESLint, Prettier, and Stylelint configurations.", "type": "module", "private": true, "version": "1.0.0", "exports": {"./eslint/base": "./eslint/base.js", "./eslint/nextjs": "./eslint/nextjs.js", "./eslint/react": "./eslint/react.js", "./prettier": "./prettier/prettier.mjs", "./stylelint": "./stylelint/config.mjs"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0"}, "dependencies": {"@cspell/dict-lorem-ipsum": "^4.0.5", "@cspell/eslint-plugin": "^9.2.0", "@eslint/js": "^9.32.0", "@ianvs/prettier-plugin-sort-imports": "^4.5.1", "@next/eslint-plugin-next": "^15.4.4", "@stylistic/eslint-plugin": "^4.4.1", "eslint-config-prettier": "^10.1.8", "eslint-config-turbo": "^2.5.5", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-storybook": "^9.0.18", "eslint-plugin-typescript-sort-keys": "^3.3.0", "globals": "^16.3.0", "prettier-plugin-tailwindcss": "^0.6.14", "stylelint-config-standard": "^38.0.0", "stylelint-config-tailwindcss": "^1.0.0", "typescript-eslint": "^8.38.0"}, "devDependencies": {"@types/eslint": "^9.6.1", "eslint": "^9.32.0", "prettier": "^3.6.2", "stylelint": "^16.22.0", "typescript": "^5.8.3"}, "peerDependencies": {"eslint": "^9.32.0", "prettier": "^3.6.2", "stylelint": "^16.20.0"}, "prettier": "./prettier/prettier.mjs"}