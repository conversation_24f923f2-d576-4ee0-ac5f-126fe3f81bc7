{"name": "@qantasexperiences/logger", "private": false, "version": "0.5.0", "type": "module", "exports": {".": "./src/index.ts", "./logger": "./src/logger/index.ts", "./routeHandlerWithLogger": "./src/routeHandlerWithLogger/index.ts"}, "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "typecheck": "tsc --noEmit", "test:unit": "jest", "test:unit:ci": "jest --coverage", "test:unit:watch": "jest --watch"}, "devDependencies": {"@babel/core": "^7.27.7", "@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/jest": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@types/jest": "^30.0.0", "jest": "^30.0.5", "next": "^15.4.4", "typescript": "^5.8.3"}, "prettier": "@qantasexperiences/code-style/prettier", "dependencies": {"pino": "^9.7.0"}, "peerDependencies": {"next": "^14.0 || ^15.0"}, "beachball": {"shouldPublish": false}, "publishConfig": {"registry": "https://repositories.services.jqdev.net/repository/npm-local/"}}