{"name": "@qantasexperiences/optimizely", "private": true, "version": "0.1.0", "type": "module", "exports": {"./server": "./src/server/index.ts", "./hooks": "./src/hooks/index.ts", "./constants": "./src/constants.ts", "./flag-hooks": "./src/flag-hooks/index.ts"}, "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "typecheck": "tsc --noEmit", "test:unit": "jest", "test:unit:ci": "jest", "test:unit:watch": "jest --watch"}, "devDependencies": {"@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/jest": "workspace:*", "@qantasexperiences/logger": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "jest": "^30.0.5", "next": "^15.4.4", "react": "^19.1.0", "typescript": "^5.8.3"}, "dependencies": {"@optimizely/optimizely-sdk": "^6.0.0", "@qantasexperiences/utils": "workspace:*", "server-only": "^0.0.1", "zod": "^3.25.67"}, "prettier": "@qantasexperiences/code-style/prettier", "peerDependencies": {"next": "^14.0 || ^15.0", "react": "^19.0"}}