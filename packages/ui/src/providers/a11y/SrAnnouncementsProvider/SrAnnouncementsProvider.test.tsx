import { act, render, screen, userEvent } from '../../../test-utils';
import { SrAnnouncementsProvider, useSrAnnouncementsContext } from './SrAnnouncementsProvider';

jest.unmock('./SrAnnouncementsProvider');

const waitForLiveRegionUpdate = () => act(() => jest.advanceTimersByTime(350));

describe('<SrAnnouncementsProvider />', () => {
  it('adds visually hidden live region for alerts', () => {
    render(<SrAnnouncementsProvider>Content</SrAnnouncementsProvider>);
    const alertRegion = screen.getByRole('alert');
    expect(alertRegion).toBeInTheDocument();
    expect(alertRegion).toHaveAttribute('aria-atomic', 'true');
    expect(alertRegion).toHaveClass('sr-only');
  });

  it('adds visually hidden live region for status updates', () => {
    render(<SrAnnouncementsProvider>Content</SrAnnouncementsProvider>);
    const statusRegion = screen.getByRole('status');
    expect(statusRegion).toBeInTheDocument();
    expect(statusRegion).toHaveAttribute('aria-atomic', 'true');
    expect(statusRegion).toHaveClass('sr-only');
  });

  it('does not have contents in live regions on initial load', () => {
    render(<SrAnnouncementsProvider>Content</SrAnnouncementsProvider>);
    expect(screen.getByRole('alert')).toBeEmptyDOMElement();
    expect(screen.getByRole('status')).toBeEmptyDOMElement();
  });
});

describe('useSrAnnouncementsContext', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('updates alert region on announceAlertUpdate', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

    const Content = () => {
      const { announceAlertUpdate } = useSrAnnouncementsContext();
      return (
        <div>
          <button type="button" onClick={() => announceAlertUpdate('Success!')}>
            Alert
          </button>
        </div>
      );
    };

    render(
      <SrAnnouncementsProvider>
        <Content />
      </SrAnnouncementsProvider>,
    );

    expect(screen.getByRole('alert')).toBeEmptyDOMElement();

    await user.click(screen.getByRole('button'));
    expect(screen.getByRole('alert')).toBeEmptyDOMElement();

    waitForLiveRegionUpdate();

    expect(screen.getByRole('alert')).toHaveTextContent('Success!');
  });

  it('updates status region on announceStatusUpdate', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

    const Content = () => {
      const { announceStatusUpdate } = useSrAnnouncementsContext();
      return (
        <div>
          <button type="button" onClick={() => announceStatusUpdate('Success!')}>
            Alert
          </button>
        </div>
      );
    };

    render(
      <SrAnnouncementsProvider>
        <Content />
      </SrAnnouncementsProvider>,
    );

    expect(screen.getByRole('status')).toBeEmptyDOMElement();

    await user.click(screen.getByRole('button'));
    expect(screen.getByRole('status')).toBeEmptyDOMElement();

    waitForLiveRegionUpdate();

    expect(screen.getByRole('status')).toHaveTextContent('Success!');
  });
});
