'use client';

import type { JSX, ReactNode } from 'react';
import { useCallback, useState } from 'react';

import { createCtx } from '@qantasexperiences/utils/context';
import { withDelay } from '@qantasexperiences/utils/general';

export interface SrAnnouncementsContextValue {
  announceAlertUpdate: (alertText: string, delay?: number) => void;
  announceStatusUpdate: (statusText: string, delay?: number) => void;
}

const [useSrAnnouncementsContext, Provider] = createCtx<SrAnnouncementsContextValue>();

export { useSrAnnouncementsContext };

export interface SrAnnouncementsProviderProps {
  children: ReactNode;
}

/**
 * This component places live regions in the dom to announce content changes for screen reader users.
 * Place this provider at or near the top of <body> outside of <main>.
 *
 * Default to using `status` (eg. loading state).
 * Only use `alert` when the content has high urgency (eg. error message).
 */
export const SrAnnouncementsProvider = ({
  children,
}: SrAnnouncementsProviderProps): JSX.Element => {
  const [alert, setAlert] = useState<string>('');
  const [status, setStatus] = useState<string>('');

  const announceAlertUpdate = useCallback((alertText: string, delay = 350) => {
    withDelay(() => setAlert(alertText), delay);
  }, []);

  const announceStatusUpdate = useCallback((statusText: string, delay = 350) => {
    withDelay(() => setStatus(statusText), delay);
  }, []);

  const value = {
    announceAlertUpdate,
    announceStatusUpdate,
  } satisfies SrAnnouncementsContextValue;

  return (
    <Provider value={value}>
      <div role="alert" aria-atomic="true" className="sr-only">
        {alert}
      </div>
      <div role="status" aria-atomic="true" className="sr-only">
        {status}
      </div>
      {children}
    </Provider>
  );
};
