import { useCallback } from 'react';

import type * as SrAnnouncementsProviderModule from '..';

const actualSrAnnouncementsProvider = jest.requireActual<typeof SrAnnouncementsProviderModule>(
  '../SrAnnouncementsProvider',
);

/**
 * This mock is used to remove delays when updating the Alert and Status live regions in the SrAnnouncementsProvider.
 *
 * The default delay is used to ensure UI elements have properly been updated before changing the live region, as UI element updates
 * (such as the closing of a modal) can cause the screen reader to miss alert/status updates. However in most cases that isn't a problem
 * when asserting the contents of the live region.
 *
 * This is intended to make tests simpler to write as we can avoid needing to excessively faking timers.
 *
 * In the case where the timing is important to test execution you can use jest.unmock to restore the original delay behaviour.
 *
 */
export const { SrAnnouncementsProvider } = actualSrAnnouncementsProvider;
export const useSrAnnouncementsContext = () => {
  const { announceAlertUpdate, announceStatusUpdate } =
    actualSrAnnouncementsProvider.useSrAnnouncementsContext();

  const announceAlertUpdateWithNoDelay = useCallback(
    (alert: string, _delay: number) => {
      announceAlertUpdate(alert, 0);
    },
    [announceAlertUpdate],
  );

  const announceStatusUpdateWithNoDelay = useCallback(
    (alert: string, _delay: number) => {
      announceStatusUpdate(alert, 0);
    },
    [announceStatusUpdate],
  );

  return {
    announceAlertUpdate: announceAlertUpdateWithNoDelay,
    announceStatusUpdate: announceStatusUpdateWithNoDelay,
  };
};
