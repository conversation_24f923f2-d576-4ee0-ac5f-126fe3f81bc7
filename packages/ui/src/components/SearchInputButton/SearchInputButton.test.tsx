import { render, screen } from '@qantasexperiences/ui/test-utils';

import { SearchInputButton } from './SearchInputButton';

describe('SearchInputButton', () => {
  it('renders no value when neither placeholder or value supplied', () => {
    render(<SearchInputButton labelText="Flying from" />, {
      brand: 'qantas',
      channel: 'hotels',
    });

    expect(screen.queryByTestId('search-input-value')).not.toBeInTheDocument();
  });

  it('renders the placeholder when value is not supplied', () => {
    render(<SearchInputButton labelText="Flying from" placeholderText="City or name" />, {
      brand: 'qantas',
      channel: 'hotels',
    });

    expect(screen.getByTestId('search-input-value')).toHaveTextContent('City or name');
  });

  it('renders the value instead of placeholder when value supplied', () => {
    render(
      <SearchInputButton
        labelText="Flying from"
        placeholderText="City or name"
        valueText="Sydney - SYD"
      />,
      {
        brand: 'qantas',
        channel: 'hotels',
      },
    );

    expect(screen.getByTestId('search-input-value')).toHaveTextContent('Sydney - SYD');
  });

  it('shows error text and has accessible description containing error message when error occurs', () => {
    render(<SearchInputButton labelText="Flying from" errorText="Please select a flying from" />, {
      brand: 'qantas',
      channel: 'hotels',
    });

    expect(screen.getByRole('button', { name: 'Flying from' })).toHaveAccessibleDescription(
      'Please select a flying from',
    );

    expect(screen.getByTestId('search-input-error')).toHaveTextContent(
      'Please select a flying from',
    );
  });
});
