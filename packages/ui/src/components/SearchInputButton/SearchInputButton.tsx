'use client';

import { useId } from 'react';

import type { Brand } from '@qantasexperiences/tenants';
import type { ClassValue } from '@qantasexperiences/utils/tailwind';
import { cn } from '@qantasexperiences/utils/tailwind';

import type { ContainerProps } from './components';
import { Container, Error, Label, Value } from './components';

export interface HolidaysSearchInputProps extends ContainerProps {
  brandOverride?: Brand;
  className?: ClassValue;
  errorText?: string;
  labelText: string;
  placeholderText?: string;
  valueText?: string;
}

export const SearchInputButton = ({
  labelText,
  valueText,
  placeholderText,
  errorText,
  className,
  ...containerProps
}: HolidaysSearchInputProps) => {
  const errorId = useId();

  return (
    <div className={cn('flex max-w-full flex-col gap-1', className)}>
      <Container
        hasError={!!errorText}
        aria-describedby={errorText ? errorId : undefined}
        {...containerProps}
      >
        <Label>{labelText}</Label>
        <Value placeholderText={placeholderText}>{valueText}</Value>
      </Container>
      {errorText && <Error id={errorId}>{errorText}</Error>}
    </div>
  );
};

SearchInputButton.Container = Container;
SearchInputButton.Label = Label;
SearchInputButton.Value = Value;
SearchInputButton.Error = Error;
