import type { ReactNode } from 'react';

import type { ClassValue } from '@qantasexperiences/utils/tailwind';
import { cn } from '@qantasexperiences/utils/tailwind';

import { SvgIcon } from '../../SvgIcon';

export interface ErrorProps {
  children?: ReactNode;
  className?: ClassValue;
  id?: string;
}

export const Error = ({ children, className, id }: ErrorProps) => {
  return (
    <span
      data-testid="search-input-error"
      className={cn(
        'text-error flex items-center overflow-hidden rounded text-left text-nowrap overflow-ellipsis',
        'q:text-body-md',
        'j:text-body-sm',
        className,
      )}
      id={id}
    >
      <SvgIcon name="error-solid" className="text-error h-4 w-4" />
      <span className="ml-1">{children}</span>
    </span>
  );
};
