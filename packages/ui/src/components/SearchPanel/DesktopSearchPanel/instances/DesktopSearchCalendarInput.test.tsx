import React from 'react';
import { parseDate } from '@internationalized/date';

import { render, screen, userEvent, within } from '@qantasexperiences/ui/test-utils';

import { SrAnnouncementsProvider } from '../../../../providers';
import { DesktopSearchCalendarInput } from './DesktopSearchCalendarInput';

describe('DesktopSearchCalendarInput', () => {
  const renderComponent = (props = {}) => {
    return render(
      <DesktopSearchCalendarInput
        label="Dates"
        placeholder="Select travel dates"
        value={{ departureDate: null, returnDate: null }}
        onChange={jest.fn()}
        onOpenChange={jest.fn()}
        {...props}
      />,
      {
        brand: 'qantas',
        channel: 'holidays',
        wrapper: SrAnnouncementsProvider,
      },
    );
  };

  it('renders label and placeholder', () => {
    renderComponent();

    const inputContainer = screen.getByTestId('date-input-lg');
    const placeholder = within(inputContainer).getByText('Select travel dates');

    expect(placeholder).toBeInTheDocument();
  });

  it('opens popover dialogue on click and displays AvailabilityCalendar', async () => {
    const user = userEvent.setup();

    renderComponent();

    const inputContainer = screen.getByTestId('date-input-lg');
    await user.click(within(inputContainer).getByText('Select travel dates'));

    const popover = screen.getByTestId('availability-calendar-popover');

    expect(popover).toBeInTheDocument();
    expect(screen.getByRole('dialog')).toHaveFocus();
  });

  it('calls onChange when dates are selected in AvailabilityCalendar', async () => {
    const onChangeMock = jest.fn();
    const onOpenChangeMock = jest.fn();
    const user = userEvent.setup();

    renderComponent({
      isOpen: true,
      onChange: onChangeMock,
      onOpenChange: onOpenChangeMock,
    });

    const inputContainer = screen.getByTestId('date-input-lg');
    await user.click(within(inputContainer).getByText('Select travel dates'));

    const startDateButton = screen.getByRole('button', { name: /First available date/ });
    await user.click(startDateButton);

    await userEvent.keyboard('[ArrowRight]');
    await userEvent.keyboard('[Enter]');

    const confirmButton = screen.getByRole('button', { name: /Confirm/i });
    await user.click(confirmButton);

    expect(onChangeMock).toHaveBeenCalled();
    expect(onOpenChangeMock).toHaveBeenCalledWith(false);
  }, 10000);

  it('calls onChange with null dates when clear dates is clicked', async () => {
    const onChangeMock = jest.fn();
    const onOpenChangeMock = jest.fn();
    const user = userEvent.setup();

    renderComponent({
      isOpen: true,
      value: { departureDate: parseDate('2024-08-01'), returnDate: parseDate('2024-08-02') },
      onChange: onChangeMock,
      onOpenChange: onOpenChangeMock,
    });

    const clearButton = screen.getByRole('button', { name: 'Clear dates' });

    await user.click(clearButton);

    expect(onChangeMock).toHaveBeenCalledWith({
      departureDate: null,
      returnDate: null,
    });
    expect(onOpenChangeMock).toHaveBeenCalledWith(true);
  });

  it('closes the AvailabilityCalendar when the input loses focus', async () => {
    const onOpenChangeMock = jest.fn();
    const user = userEvent.setup();

    renderComponent({
      isOpen: true,
      onOpenChange: onOpenChangeMock,
    });

    const inputContainer = screen.getByTestId('date-input-lg');

    await user.click(within(inputContainer).getByText('Select travel dates'));
    await user.click(document.body);

    expect(onOpenChangeMock).toHaveBeenCalledWith(false);
    expect(inputContainer).not.toHaveFocus();
    expect(screen.getByRole('dialog')).toHaveFocus();
  });

  it('closes AvailabilityCalendar when confirm button is clicked', async () => {
    const onChangeMock = jest.fn();
    const onOpenChangeMock = jest.fn();
    const user = userEvent.setup();

    renderComponent({
      isOpen: true,
      onChange: onChangeMock,
      onOpenChange: onOpenChangeMock,
    });

    const confirmButton = screen.getByRole('button', { name: /Confirm/i });
    await user.click(confirmButton);

    expect(onOpenChangeMock).toHaveBeenCalledWith(false);
  });
});
