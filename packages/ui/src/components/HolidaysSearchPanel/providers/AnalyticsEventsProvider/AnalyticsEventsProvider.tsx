import { createContext, useContext } from 'react';

import type {
  CTAClickEventOptions,
  SearchFilterStateValues,
  SearchSortStateValue,
  SearchSubmitStateValue,
} from '../../types';

export interface SortAndFilterEvents {
  sendFilterApplyEvent?: (filterValues: SearchFilterStateValues) => void;
  sendFilterOpenEvent?: () => void;
  sendModalCloseEvent?: (componentVariant: 'sort' | 'filter') => void;
  sendSortApplyEvent?: (sortValue: SearchSortStateValue) => void;
  sendSortOpenEvent?: () => void;
  sendSortSelectEvent?: (sortValue: NonNullable<SearchSortStateValue>) => void;
  sendTabClickEvent?: (componentVariant: 'sort' | 'filter') => void;
}

export interface SearchPanelMapEvents {
  sendMapOpenClickEvent?: (isDesktop?: boolean) => void;
}

export interface SearchPanelFieldEvents {
  sendCtaClickEvent?: (ctaClickEventOptions: CTAClickEventOptions) => void;
  sendSearchExitFieldEvent?: (searchExitFieldEventOptions: {
    fieldName: string;
    isDesktop: boolean;
    value: string;
  }) => void;
  sendSearchSubmitEvent?: (submitValue: SearchSubmitStateValue) => void;
}

export type HolidaysSearchPanelAnalyticsEvents = SortAndFilterEvents &
  SearchPanelFieldEvents &
  SearchPanelMapEvents;

const AnalyticsEventsContext = createContext<HolidaysSearchPanelAnalyticsEvents>({});

export const useAnalyticsEvents = () => useContext(AnalyticsEventsContext);

export const AnalyticsEventsProvider = ({
  children,
  value,
}: {
  children: React.ReactNode;
  value: HolidaysSearchPanelAnalyticsEvents | undefined;
}) => {
  return (
    <AnalyticsEventsContext.Provider value={value ?? {}}>
      {children}
    </AnalyticsEventsContext.Provider>
  );
};
