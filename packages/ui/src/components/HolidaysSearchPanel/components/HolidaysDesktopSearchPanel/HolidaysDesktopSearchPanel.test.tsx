import { getLocalTimeZone, toCalendarDate, today } from '@internationalized/date';

import type { SearchPageSanityCampaign } from '@qantasexperiences/sanity/api';
import { render, screen, userEvent, waitFor } from '@qantasexperiences/ui/test-utils';

import type { OriginAirport } from '../../types';
import { SrAnnouncementsProvider } from '../../../../providers';
import { HolidaysDesktopSearchPanel } from './HolidaysDesktopSearchPanel';

jest.mock('@qantasexperiences/data/client', () => ({
  useBlackoutDates: jest.fn().mockReturnValue({
    blackoutDates: {
      return: {},
      departure: {},
    },
  }),
}));

const INITIAL_STATE = {
  adults: 2,
  children: null,
  departureDate: null,
  destination: null,
  infants: null,
  originCode: null,
  returnDate: null,
};

const OOL = {
  name: 'Gold Coast',
  code: '<PERSON><PERSON>',
  alias: null,
  type: 'Regional',
  displayName: 'Gold Coast - OOL',
} satisfies OriginAirport;

const BNE = {
  name: 'Brisbane',
  code: 'BNE',
  alias: null,
  type: 'Major City',
  displayName: 'Brisbane - BNE',
} satisfies OriginAirport;

const SYDNEY = {
  name: 'Sydney',
  slug: 'sydney',
  searchTags: null,
  airport: {
    name: 'Sydney Kingsford Smith',
    code: 'SYD',
    alias: 'Sydney',
    displayName: 'Sydney - SYD',
  },
  originCodes: ['ADL', 'PER', 'DRW', 'HBA', 'AVV', 'BNE', 'OOL'],
  state: 'New South Wales',
  country: 'Australia',
};

const MELBOURNE = {
  name: 'Melbourne',
  slug: 'melbourne',
  searchTags: null,
  airport: {
    name: 'Melbourne (Tullamarine)',
    code: 'MEL',
    alias: 'Melbourne',
    displayName: 'Melbourne - MEL',
  },
  originCodes: ['SYD', 'ADL', 'PER', 'DRW', 'OOL'],
  state: 'Victoria',
  country: 'Australia',
};

const SOME_DESTINATIONS_CAMPAIGN = {
  endDate: '2026-12-14T13:37:00Z',
  activeInAllDestinations: false,
  activeDestinations: ['melbourne', 'sydney', 'gold-coast'],
  flights: [
    {
      origin: 'OOL',
      destination: 'SYD',
      travelDates: [{ start: '2024-01-01', end: '2024-01-10' }],
    },
  ],
  calendar: {
    title: 'Some Destinations Campaign',
    noOriginMessage: 'No Origin',
    unsupportedRouteMessage: 'Route unsupported',
  },
  sashing: { flightPromoCode: 'SALE', flightPillText: 'Big Sale!' },
  banner: { text: 'Big Sale On Now!', termsConditions: 'Big Sale TnCs' },
} satisfies SearchPageSanityCampaign;

describe('HolidaysDesktopSearchPanel', () => {
  it('renders provided actions', () => {
    render(
      <HolidaysDesktopSearchPanel
        actions={<button>My Custom Action</button>}
        destinations={[]}
        origins={[]}
        values={INITIAL_STATE}
        setValues={jest.fn()}
        hasSearched={false}
      />,
      { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
    );

    expect(screen.getByRole('button', { name: 'My Custom Action' })).toBeInTheDocument();
  });

  it('initialises inputs with provided values', () => {
    render(
      <HolidaysDesktopSearchPanel
        destinations={[SYDNEY]}
        origins={[OOL]}
        values={{
          ...INITIAL_STATE,
          originCode: 'OOL',
          destination: 'sydney',
          adults: 2,
          children: 0,
          infants: 0,
        }}
        setValues={jest.fn()}
        hasSearched={false}
        actions={<button>Search</button>}
      />,
      { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
    );

    const originInput = screen.getByLabelText('Flying from');

    expect(originInput).toHaveValue('Gold Coast - OOL');

    const destinationInput = screen.getByLabelText('Travelling to');

    expect(destinationInput).toHaveValue('Sydney');

    const travellersInput = screen.getByText('2 travellers');

    expect(travellersInput).toBeInTheDocument();
  });

  it('lets you search and select an origin', async () => {
    const user = userEvent.setup();
    const setValues = jest.fn();

    render(
      <HolidaysDesktopSearchPanel
        destinations={[]}
        origins={[OOL, BNE]}
        values={INITIAL_STATE}
        setValues={setValues}
        hasSearched={false}
        actions={<button>Search</button>}
      />,
      { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
    );

    const destinationInput = screen.getByLabelText('Flying from');
    await user.click(destinationInput);

    // Show both options in their 2 sections on focus
    expect(screen.getAllByRole('group')).toHaveLength(2);
    expect(screen.getAllByRole('option')).toHaveLength(2);

    // Filters down to 1 option with no sections on search
    await user.type(destinationInput, 'gol');

    expect(screen.queryByRole('group')).not.toBeInTheDocument();

    const oolOption = screen.getByRole('option');

    expect(oolOption).toHaveTextContent('Gold Coast - OOL');

    await user.click(oolOption);

    expect(destinationInput).toHaveValue('Gold Coast - OOL');
    expect(setValues).toHaveBeenCalledWith({
      originCode: 'OOL',
      destination: null,
    });
  });

  it('lets you search and select a destination', async () => {
    const user = userEvent.setup();
    const setValues = jest.fn();

    render(
      <HolidaysDesktopSearchPanel
        destinations={[SYDNEY, MELBOURNE]}
        origins={[OOL]}
        values={{ ...INITIAL_STATE, originCode: 'OOL' }}
        setValues={setValues}
        hasSearched={false}
        actions={<button>Search</button>}
      />,
      { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
    );

    const destinationInput = screen.getByLabelText('Travelling to');
    await user.click(destinationInput);

    // Show both options in their 2 sections on focus
    expect(screen.getAllByRole('group')).toHaveLength(2);
    expect(screen.getAllByRole('option')).toHaveLength(2);

    // Filters down to 1 option with no sections on search
    await user.type(destinationInput, 'syd');

    expect(screen.queryByRole('group')).not.toBeInTheDocument();

    const oolOption = screen.getByRole('option');

    expect(oolOption).toHaveTextContent('Sydney');

    await user.click(oolOption);

    expect(destinationInput).toHaveValue('Sydney');
    expect(setValues).toHaveBeenCalledWith({ destination: 'sydney' });
  });

  it('renders Travellers Counter component when clicked in the input', async () => {
    const user = userEvent.setup();
    const setValues = jest.fn();

    render(
      <HolidaysDesktopSearchPanel
        destinations={[SYDNEY, MELBOURNE]}
        origins={[OOL]}
        values={{ ...INITIAL_STATE, adults: 2, children: 0, infants: 0 }}
        setValues={setValues}
        hasSearched={false}
        actions={<button>Search</button>}
      />,
      { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
    );

    const travellersCounter = screen.getByRole('button', { name: 'Travellers 2 travellers' });
    await user.click(travellersCounter);

    // Check for the Increase / Decrease  button for Adults
    expect(screen.getByRole('button', { name: /Increase Adults/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Decrease Adults/i })).toBeInTheDocument();

    // Check for the Increase / Decrease button for Children
    expect(screen.getByRole('button', { name: /Increase Children/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Decrease Children/i })).toBeInTheDocument();

    // Check for the Increase / Decrease button for Infants
    expect(screen.getByRole('button', { name: /Increase Infants/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Decrease Infants/i })).toBeInTheDocument();
  });

  it('shows unavailable message when there are no matching items for origins', async () => {
    const user = userEvent.setup();

    render(
      <HolidaysDesktopSearchPanel
        destinations={[SYDNEY]}
        origins={[OOL]}
        values={INITIAL_STATE}
        setValues={jest.fn()}
        hasSearched={false}
        actions={<button>Search</button>}
      />,
      { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
    );

    await user.type(screen.getByLabelText('Flying from'), 'this will not find any origins');

    expect(screen.getByTestId('empty-message')).toHaveTextContent(
      "Sorry, we couldn't find any matches for 'this will not find any origins'Check your spelling or try a different location",
    );
  });

  it('shows unavailable message when there are no matching items for destinations', async () => {
    const user = userEvent.setup();

    render(
      <HolidaysDesktopSearchPanel
        destinations={[SYDNEY]}
        origins={[OOL]}
        values={{ ...INITIAL_STATE, originCode: 'OOL' }}
        setValues={jest.fn()}
        hasSearched={false}
        actions={<button>Search</button>}
      />,
      { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
    );

    await user.type(screen.getByLabelText('Travelling to'), 'this will not find any destinations');

    expect(screen.getByTestId('empty-message')).toHaveTextContent(
      "Sorry, we couldn't find any matches for 'this will not find any destinations'Check your spelling or try a different location",
    );
  });

  it('shows an underlay when interacting with any of the inputs if showUnderlay is true', async () => {
    const user = userEvent.setup();

    const { container } = render(
      <HolidaysDesktopSearchPanel
        showUnderlay
        destinations={[SYDNEY, MELBOURNE]}
        origins={[OOL]}
        values={{
          ...INITIAL_STATE,
          originCode: 'OOL',
          destination: 'sydney',
        }}
        setValues={jest.fn()}
        hasSearched={false}
        actions={<button>Search</button>}
      />,
      { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
    );

    // Initially no underlay
    expect(screen.queryByTestId('search-underlay')).not.toBeInTheDocument();

    // Interact => underlay
    await user.click(screen.getByLabelText('Flying from'));

    expect(screen.queryByTestId('search-underlay')).toBeInTheDocument();

    // Interact => underlay
    await user.type(screen.getByLabelText('Travelling to'), 'a');

    expect(screen.queryByTestId('search-underlay')).toBeInTheDocument();

    // Interact outside => no underlay
    await user.click(container);

    expect(screen.queryByTestId('search-underlay')).not.toBeInTheDocument();

    // Interact => underlay
    await user.click(screen.getByRole('button', { name: /When/ }));

    expect(screen.queryByTestId('search-underlay')).toBeInTheDocument();

    // Interact outside => refocuses on 'When' button
    await user.click(container);
    await waitFor(() => expect(screen.getByRole('button', { name: /When/ })).toHaveFocus());

    // Interact outside => no underlay
    await user.click(container);

    expect(screen.queryByTestId('search-underlay')).not.toBeInTheDocument();

    // Interact => underlay
    await user.click(screen.getByRole('button', { name: /Travellers/ }));

    expect(screen.queryByTestId('search-underlay')).toBeInTheDocument();

    // Interact outside => refocuses on 'Travellers' button
    await user.click(container);
    await waitFor(() => expect(screen.getByRole('button', { name: /Travellers/ })).toHaveFocus());

    // Interact outside => no underlay
    await user.click(container);

    expect(screen.queryByTestId('search-underlay')).not.toBeInTheDocument();
  });

  it('does not show an underlay when interacting with any of the inputs if showUnderlay is false', async () => {
    const user = userEvent.setup();

    render(
      <HolidaysDesktopSearchPanel
        destinations={[SYDNEY, MELBOURNE]}
        origins={[OOL]}
        values={{
          ...INITIAL_STATE,
          originCode: 'OOL',
          destination: 'sydney',
        }}
        setValues={jest.fn()}
        hasSearched={false}
        actions={<button>Search</button>}
      />,
      { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
    );

    // Interact => no underlay
    await user.click(screen.getByLabelText('Flying from'));

    expect(screen.queryByTestId('search-underlay')).not.toBeInTheDocument();

    // Interact => no underlay
    await user.type(screen.getByLabelText('Travelling to'), 'a');

    expect(screen.queryByTestId('search-underlay')).not.toBeInTheDocument();
  });

  it('availability calendar popover is properly rendered on click', async () => {
    const user = userEvent.setup();
    const setValues = jest.fn();

    render(
      <HolidaysDesktopSearchPanel
        destinations={[SYDNEY, MELBOURNE]}
        origins={[OOL]}
        values={{ ...INITIAL_STATE, originCode: 'OOL', destination: 'sydney' }}
        setValues={setValues}
        hasSearched={false}
        actions={<button>Search</button>}
      />,
      { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
    );

    const whenButton = screen.getByRole('button', {
      name: /When/,
    });
    await user.click(whenButton);

    expect(screen.getByTestId('availability-calendar-popover')).toBeInTheDocument();
  });

  it('shows campaign calendar information based on selected origin and destination', async () => {
    const user = userEvent.setup();
    const setValues = jest.fn();

    render(
      <HolidaysDesktopSearchPanel
        destinations={[SYDNEY, MELBOURNE]}
        origins={[OOL]}
        values={{ ...INITIAL_STATE, originCode: 'OOL', destination: 'sydney' }}
        setValues={setValues}
        hasSearched={false}
        actions={<button>Search</button>}
        campaigns={[SOME_DESTINATIONS_CAMPAIGN]}
      />,
      { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
    );

    const whenButton = screen.getByRole('button', {
      name: /When/,
    });
    await user.click(whenButton);

    expect(screen.getByText('Some Destinations Campaign')).toBeInTheDocument();
    expect(screen.getByText('Travel between Jan 1 – 10, 2024')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'View Big Sale! - sale terms' })).toBeInTheDocument();
  });

  describe('validation', () => {
    it('disables subsequent fields (excluding travellers), until previous fields are valid', () => {
      const { rerender } = render(
        <HolidaysDesktopSearchPanel
          destinations={[SYDNEY, MELBOURNE]}
          origins={[OOL]}
          values={{ ...INITIAL_STATE }}
          setValues={jest.fn()}
          hasSearched={false}
          actions={<button>Search</button>}
        />,
        { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
      );

      const travellingTo = screen.getByLabelText('Travelling to');
      const when = screen.getByRole('button', {
        name: 'When Select travel dates Select travel dates',
      });
      const travellers = screen.getByRole('button', {
        name: 'Travellers 2 travellers',
      });

      // Flying from invalid => all following fields invalid
      expect(travellingTo).toBeDisabled();
      expect(when).toBeDisabled();
      expect(travellers).not.toBeDisabled();

      rerender(
        <HolidaysDesktopSearchPanel
          destinations={[SYDNEY, MELBOURNE]}
          origins={[OOL]}
          values={{ ...INITIAL_STATE, originCode: 'OOL' }}
          setValues={jest.fn()}
          hasSearched={false}
          actions={<button>Search</button>}
        />,
      );

      // Flying from valid => travelling to now enabled
      expect(travellingTo).not.toBeDisabled();
      expect(when).toBeDisabled();
      expect(travellers).not.toBeDisabled();

      rerender(
        <HolidaysDesktopSearchPanel
          destinations={[SYDNEY, MELBOURNE]}
          origins={[OOL]}
          values={{ ...INITIAL_STATE, originCode: 'OOL', destination: 'sydney' }}
          setValues={jest.fn()}
          hasSearched={false}
          actions={<button>Search</button>}
        />,
      );

      // Travelling to valid => when now enabled
      expect(travellingTo).not.toBeDisabled();
      expect(when).not.toBeDisabled();
      expect(travellers).not.toBeDisabled();
    });

    it('shows error and updates live region when form is dirty. Error is hidden and live region alert is removed while a field is focused but returns on blur.', async () => {
      const user = userEvent.setup();

      const { container } = render(
        <HolidaysDesktopSearchPanel
          destinations={[SYDNEY, MELBOURNE]}
          origins={[OOL]}
          values={INITIAL_STATE}
          setValues={jest.fn()}
          hasSearched={true}
          actions={<button>Search</button>}
        />,
        { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
      );

      const getFlyingFromError = () =>
        screen.queryByRole('tooltip', {
          name: 'Please select where you are flying from',
        });

      expect(getFlyingFromError()).toBeInTheDocument();
      expect(screen.getByRole('alert').textContent).toBe('Please select where you are flying from');

      await user.click(screen.getByLabelText('Flying from'));

      expect(getFlyingFromError()).not.toBeInTheDocument();
      expect(screen.getByRole('alert', { hidden: true })).toBeEmptyDOMElement();

      await user.click(container);

      expect(getFlyingFromError()).toBeInTheDocument();
      expect(screen.getByRole('alert').textContent).toBe('Please select where you are flying from');
    });

    // TODO: Not currently used logic, will need to re-think how to properly implement when it is required
    // eslint-disable-next-line jest/no-disabled-tests
    it.skip(
      'if the form is not dirty, it shows an error when a user interacts with a disabled field. ' +
        'This error disappears after the user interacts with a valid field.',
      async () => {
        const user = userEvent.setup();

        const { container } = render(
          <HolidaysDesktopSearchPanel
            destinations={[SYDNEY, MELBOURNE]}
            origins={[OOL]}
            values={INITIAL_STATE}
            setValues={jest.fn()}
            hasSearched={false}
            actions={<button>Search</button>}
          />,
          { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
        );

        const getFlyingFromError = () =>
          screen.queryByRole('tooltip', {
            name: 'Please select where you are flying from',
          });

        expect(getFlyingFromError()).not.toBeInTheDocument();

        await user.click(screen.getByText('Travelling to'));

        expect(getFlyingFromError()).toBeInTheDocument();

        await user.click(screen.getByLabelText('Flying from'));
        await user.click(container);

        expect(getFlyingFromError()).not.toBeInTheDocument();
      },
    );

    it('shows error message and updates live region 1 field at a time in left to right order', () => {
      const { rerender } = render(
        <HolidaysDesktopSearchPanel
          destinations={[SYDNEY, MELBOURNE]}
          origins={[OOL]}
          values={{ ...INITIAL_STATE }}
          setValues={jest.fn()}
          hasSearched={true}
          actions={<button>Search</button>}
        />,
        { brand: 'jetstar', channel: 'holidays', wrapper: SrAnnouncementsProvider },
      );

      expect(screen.queryByRole('tooltip')).toHaveTextContent(
        'Please select where you are flying from',
      );
      expect(screen.getByRole('alert').textContent).toBe('Please select where you are flying from');

      rerender(
        <HolidaysDesktopSearchPanel
          destinations={[SYDNEY, MELBOURNE]}
          origins={[OOL]}
          values={{ ...INITIAL_STATE, originCode: 'OOL' }}
          setValues={jest.fn()}
          hasSearched={true}
          actions={<button>Search</button>}
        />,
      );

      expect(screen.queryByRole('tooltip')).toHaveTextContent(
        'Please select where you are travelling to',
      );
      expect(screen.getByRole('alert').textContent).toBe(
        'Please select where you are travelling to',
      );

      rerender(
        <HolidaysDesktopSearchPanel
          destinations={[SYDNEY, MELBOURNE]}
          origins={[OOL]}
          values={{ ...INITIAL_STATE, originCode: 'OOL', destination: 'sydney' }}
          setValues={jest.fn()}
          hasSearched={true}
          actions={<button>Search</button>}
        />,
      );

      expect(screen.queryByRole('tooltip')).toHaveTextContent(
        'Please select when you want to travel',
      );
      expect(screen.getByRole('alert').textContent).toBe('Please select when you want to travel');

      const now = today(getLocalTimeZone());
      const departureDate = now.add({ days: 4 });
      const returnDate = departureDate.add({ days: 7 });

      rerender(
        <HolidaysDesktopSearchPanel
          destinations={[SYDNEY, MELBOURNE]}
          origins={[OOL]}
          values={{
            ...INITIAL_STATE,
            originCode: 'OOL',
            destination: 'sydney',
            departureDate: toCalendarDate(departureDate).toString(),
            returnDate: toCalendarDate(returnDate).toString(),
          }}
          setValues={jest.fn()}
          hasSearched={true}
          actions={<button>Search</button>}
        />,
      );

      // TODO: Implementing validation for travellers to be discussed. Travellers always have default value of 2
      expect(screen.queryByRole('tooltip')).toBeNull();
    });
  });
});
