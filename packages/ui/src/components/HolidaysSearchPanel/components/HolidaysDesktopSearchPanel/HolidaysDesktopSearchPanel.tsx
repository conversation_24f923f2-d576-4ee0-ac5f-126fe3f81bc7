'use client';

import type { ReactNode } from 'react';
import { useEffect, useMemo, useState } from 'react';
import { UNSAFE_PortalProvider } from 'react-aria';

import type { SearchPageSanityCampaign } from '@qantasexperiences/sanity/api';

import type {
  Destination,
  OriginAirport,
  SearchPanelStateSetter,
  SearchPanelStateValues,
} from '../../types';
import { useSrAnnouncementsContext } from '../../../../providers';
import { DesktopSearchPanel } from '../../../SearchPanel';
import { useIsDesktopSearchPanelActive, useSanitisedData, useValidation } from '../../hooks';
import {
  FlyingFromInput,
  TravelDatesInput,
  TravellersInput,
  TravellingToInput,
} from './components';

export interface HolidaysDesktopSearchPanelProps {
  actions: ReactNode;
  campaigns?: SearchPageSanityCampaign[] | null;
  destinations: Destination[];
  hasSearched: boolean;
  origins: OriginAirport[];
  setValues: SearchPanelStateSetter;
  showUnderlay?: boolean;
  values: SearchPanelStateValues;
}

export const HolidaysDesktopSearchPanel = ({
  destinations: destinationsProp,
  hasSearched,
  origins: originsProp,
  setValues,
  values: valuesProp,
  campaigns,
  showUnderlay,
  actions,
}: HolidaysDesktopSearchPanelProps) => {
  const [container, setContainer] = useState<HTMLDivElement | null>(null);
  const { isDesktopSearchPanelActive, inputsContainerProps } = useIsDesktopSearchPanelActive();
  const { announceAlertUpdate } = useSrAnnouncementsContext();

  const { origins, values, destinations } = useSanitisedData({
    values: valuesProp,
    destinations: destinationsProp,
    origins: originsProp,
  });
  const { prioritisedError, fieldDisabledState } = useValidation(values);
  const errorMessages = useMemo(
    () => (hasSearched && !isDesktopSearchPanelActive ? prioritisedError : {}),
    [hasSearched, isDesktopSearchPanelActive, prioritisedError],
  );

  useEffect(() => {
    // Get error string by getting first property value,
    // as the errorMessages object is filtered to only have one property
    const errorString = Object.values(errorMessages)[0];
    if (errorString) {
      announceAlertUpdate(errorString);
    } else {
      announceAlertUpdate('', 0);
    }
  }, [announceAlertUpdate, errorMessages]);

  return (
    <UNSAFE_PortalProvider getContainer={() => container}>
      <div className="w-full" ref={setContainer}>
        {isDesktopSearchPanelActive && showUnderlay && (
          <div
            data-testid="search-underlay"
            className="bg-overlay animate-in fade-in fixed inset-0 opacity-80"
          />
        )}
        <DesktopSearchPanel.Container
          className="isolate"
          inputsContainerProps={{
            ...inputsContainerProps,
            className: '[&>*]:max-w-[230px]',
          }}
          inputs={
            <>
              <FlyingFromInput
                error={errorMessages.flyingFrom}
                isDisabled={fieldDisabledState.flyingFrom}
                origins={origins}
                setValues={setValues}
                values={values}
              />
              <TravellingToInput
                destinations={destinations}
                error={errorMessages.travellingTo}
                isDisabled={fieldDisabledState.travellingTo}
                setValues={setValues}
                values={values}
              />
              <TravelDatesInput
                destinations={destinations}
                error={errorMessages.dates}
                isDisabled={fieldDisabledState.dates}
                setValues={setValues}
                values={values}
                campaigns={campaigns}
              />
              <TravellersInput setValues={setValues} values={values} />
            </>
          }
          actions={actions}
        />
      </div>
    </UNSAFE_PortalProvider>
  );
};
