import type { ModalProps } from '../../../Modal';
import type { FilterPanelProps, SortPanelProps } from './components';
import { Modal } from '../../../Modal';
import { Tabs } from '../../../Tabs';
import { useAnalyticsEvents } from '../../providers/AnalyticsEventsProvider';
import { FilterPanel, SortPanel } from './components';
import { useCtaProps } from './utils/useCtaProps';

const FILTER_TAB_CONFIG = {
  id: 'filter',
  title: 'Filter',
} as const;

const SORT_TAB_CONFIG = {
  id: 'sort',
  title: 'Sort',
} as const;

const MODAL_TABS = [FILTER_TAB_CONFIG, SORT_TAB_CONFIG];

export type SortAndFilterModalTabKey = (typeof MODAL_TABS)[number]['id'];

export interface HolidaysSortAndFilterModalProps extends FilterPanelProps, SortPanelProps {
  isOpen: NonNullable<ModalProps['isOpen']>;
  onChangeTab: (tab: SortAndFilterModalTabKey) => void;
  onClearFilters: () => void;
  onClearSort: () => void;
  onOpenChange: NonNullable<ModalProps['onOpenChange']>;
  onSubmit: () => void;
  selectedTab: SortAndFilterModalTabKey;
}

export const HolidaysSortAndFilterModal = ({
  isOpen,
  onOpenChange,
  selectedTab,
  onChangeTab,
  filterFieldsMeta,
  isFilterMetaLoading,
  filterValues,
  onFilterValuesChange,
  sortValue,
  onSortValueChange,
  onClearFilters,
  onClearSort,
  onSubmit,
}: HolidaysSortAndFilterModalProps) => {
  const { primaryCta, tertiaryCta } = useCtaProps({
    selectedTab,
    onClearSort,
    onSubmit,
    onClearFilters,
    sortValue,
    filterValues,
  });

  const { sendModalCloseEvent, sendTabClickEvent } = useAnalyticsEvents();

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      sendModalCloseEvent?.(selectedTab);
    }
    onOpenChange(open);
  };

  const handleTabChange = (key: SortAndFilterModalTabKey) => {
    sendTabClickEvent?.(key);
    onChangeTab(key);
  };

  return (
    <Modal
      size="large"
      title="Filter & Sort"
      mobileVariant="tray-full-screen"
      mobileVariantBreakpoint="lg"
      isOpen={isOpen}
      onOpenChange={handleOpenChange}
      primaryCta={primaryCta}
      tertiaryCta={tertiaryCta}
    >
      <Tabs
        selectedKey={selectedTab}
        onSelectionChange={(key) => handleTabChange(key as SortAndFilterModalTabKey)}
      >
        <Tabs.List label="Filter and Sort" items={MODAL_TABS} isEqualWidth />
        <div className="grid grid-cols-1 grid-rows-1 p-4 lg:p-6">
          <Tabs.Panel
            id={FILTER_TAB_CONFIG.id}
            className="col-span-full row-span-full data-[inert=true]:invisible"
            shouldForceMount
          >
            <FilterPanel
              isFilterMetaLoading={isFilterMetaLoading}
              filterValues={filterValues}
              onFilterValuesChange={onFilterValuesChange}
              filterFieldsMeta={filterFieldsMeta}
            />
          </Tabs.Panel>
          <Tabs.Panel
            id={SORT_TAB_CONFIG.id}
            className="col-span-full row-span-full data-[inert=true]:invisible"
            shouldForceMount
          >
            <SortPanel sortValue={sortValue} onSortValueChange={onSortValueChange} />
          </Tabs.Panel>
        </div>
      </Tabs>
    </Modal>
  );
};
