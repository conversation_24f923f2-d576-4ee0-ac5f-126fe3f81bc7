import { useState } from 'react';

import { render, screen, userEvent } from '@qantasexperiences/ui/test-utils';

import type { FilterFieldsMeta, SearchFilterStateValues, SearchSortStateValue } from '../../types';
import type {
  HolidaysSortAndFilterModalProps,
  SortAndFilterModalTabKey,
} from './HolidaysSortAndFilterModal';
import { HolidaysSortAndFilterModal } from './HolidaysSortAndFilterModal';

const mockSendSortApplyEvent = jest.fn();
const mockSendFilterApplyEvent = jest.fn();
const mockSendModalCloseEvent = jest.fn();
const mockSendTabClickEvent = jest.fn();
jest.mock('../../providers/AnalyticsEventsProvider', () => ({
  useAnalyticsEvents: () => ({
    sendSortApplyEvent: mockSendSortApplyEvent,
    sendFilterApplyEvent: mockSendFilterApplyEvent,
    sendModalCloseEvent: mockSendModalCloseEvent,
    sendTabClickEvent: mockSendTabClickEvent,
  }),
}));

const DEFAULT_FILTER_VALUES = {
  minPrice: 100,
  maxPrice: 500,
  propertyRatings: [3, 4],
} satisfies SearchFilterStateValues;

const DEFAULT_FILTER_FIELDS_META = {
  minPrice: 50,
  maxPrice: 600,
} satisfies FilterFieldsMeta;

const DEFAULT_SORT_VALUE = null satisfies SearchSortStateValue;

const TestHolidaysSortAndFilterModal = (props: Partial<HolidaysSortAndFilterModalProps>) => {
  const {
    onFilterValuesChange = jest.fn(),
    onClearFilters = jest.fn(),
    onClearSort = jest.fn(),
    onSubmit = jest.fn(),
    isOpen: isOpenProp = false,
    selectedTab = 'filter',
    onSortValueChange = jest.fn(),
    sortValue = DEFAULT_SORT_VALUE,
    onOpenChange: onOpenChangeProp,
  } = props;
  const [tab, setTab] = useState<SortAndFilterModalTabKey>(selectedTab);
  const [isOpen, setIsOpen] = useState(isOpenProp);

  const openModal = (tabName: SortAndFilterModalTabKey) => {
    setTab(tabName);
    setIsOpen(true);
  };

  const handleOpenChange = (open: boolean) => {
    if (onOpenChangeProp) {
      onOpenChangeProp(open);
    } else {
      setIsOpen(open);
    }
  };

  return (
    <>
      <button onClick={() => openModal('filter')}>open with filter tab</button>
      <button onClick={() => openModal('sort')}>open with sort tab</button>
      <HolidaysSortAndFilterModal
        selectedTab={tab}
        onChangeTab={setTab}
        onOpenChange={handleOpenChange}
        isOpen={isOpen}
        filterValues={DEFAULT_FILTER_VALUES}
        filterFieldsMeta={DEFAULT_FILTER_FIELDS_META}
        isFilterMetaLoading={false}
        onFilterValuesChange={onFilterValuesChange}
        onClearSort={onClearSort}
        onClearFilters={onClearFilters}
        onSubmit={onSubmit}
        onSortValueChange={onSortValueChange}
        sortValue={sortValue}
      />
    </>
  );
};

describe('HolidaysSortAndFilterModal', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('opens with the filter tab active when selected', async () => {
    const user = userEvent.setup();
    render(<TestHolidaysSortAndFilterModal />, { brand: 'qantas', channel: 'holidays' });

    await user.click(screen.getByRole('button', { name: 'open with filter tab' }));

    expect(screen.getByRole('tab', { name: 'Filter' })).toHaveAttribute('aria-selected', 'true');
    expect(screen.getByRole('tabpanel')).toHaveTextContent('Total price (Flights + Hotel)');
  });

  it('opens with the sort tab active when selected', async () => {
    const user = userEvent.setup();
    render(<TestHolidaysSortAndFilterModal />, { brand: 'qantas', channel: 'holidays' });

    await user.click(screen.getByRole('button', { name: 'open with sort tab' }));

    expect(screen.getByRole('tab', { name: 'Sort' })).toHaveAttribute('aria-selected', 'true');
    expect(screen.getByRole('tabpanel')).toHaveTextContent('Sort by');
  });

  it('swaps between the Filter and Sort tabs', async () => {
    const user = userEvent.setup();
    render(<TestHolidaysSortAndFilterModal isOpen />, { brand: 'qantas', channel: 'holidays' });

    expect(screen.getByRole('tab', { name: 'Filter' })).toHaveAttribute('aria-selected', 'true');
    expect(screen.getByRole('tab', { name: 'Sort' })).toHaveAttribute('aria-selected', 'false');
    expect(screen.getByRole('tabpanel')).toHaveTextContent('Total price (Flights + Hotel)');

    await user.click(screen.getByRole('tab', { name: 'Sort' }));

    expect(screen.getByRole('tab', { name: 'Sort' })).toHaveAttribute('aria-selected', 'true');
    expect(screen.getByRole('tab', { name: 'Filter' })).toHaveAttribute('aria-selected', 'false');
    expect(screen.getByRole('tabpanel')).toHaveTextContent('Sort by');

    await user.click(screen.getByRole('tab', { name: 'Filter' }));

    expect(screen.getByRole('tab', { name: 'Filter' })).toHaveAttribute('aria-selected', 'true');
    expect(screen.getByRole('tab', { name: 'Sort' })).toHaveAttribute('aria-selected', 'false');
    expect(screen.getByRole('tabpanel')).toHaveTextContent('Total price (Flights + Hotel)');
  });

  it('calls sendTabClickEvent when tabs are clicked', async () => {
    const user = userEvent.setup();
    render(<TestHolidaysSortAndFilterModal isOpen />, { brand: 'qantas', channel: 'holidays' });

    await user.click(screen.getByRole('tab', { name: 'Sort' }));
    expect(mockSendTabClickEvent).toHaveBeenCalledWith('sort');

    await user.click(screen.getByRole('tab', { name: 'Filter' }));
    expect(mockSendTabClickEvent).toHaveBeenCalledWith('filter');
  });

  describe('Filter Tab', () => {
    it('shows the "Clear all" and "Show packages" CTA buttons', () => {
      render(<TestHolidaysSortAndFilterModal isOpen selectedTab="filter" />, {
        brand: 'qantas',
        channel: 'holidays',
      });

      expect(screen.getByRole('button', { name: 'Clear all' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Show packages' })).toBeInTheDocument();
    });

    it('calls onSubmit when "Show packages" button is clicked', async () => {
      const user = userEvent.setup();
      const onSubmitMock = jest.fn();

      render(
        <TestHolidaysSortAndFilterModal isOpen selectedTab="filter" onSubmit={onSubmitMock} />,
        {
          brand: 'qantas',
          channel: 'holidays',
        },
      );

      await user.click(screen.getByRole('button', { name: 'Show packages' }));

      expect(onSubmitMock).toHaveBeenCalled();
    });

    it('calls sendFilterApplyEvent with filterValues when "Show packages" button is clicked', async () => {
      const user = userEvent.setup();

      render(
        <TestHolidaysSortAndFilterModal
          isOpen
          selectedTab="filter"
          filterValues={DEFAULT_FILTER_VALUES}
          onSubmit={jest.fn()}
        />,
        {
          brand: 'qantas',
          channel: 'holidays',
        },
      );

      await user.click(screen.getByRole('button', { name: 'Show packages' }));

      expect(mockSendFilterApplyEvent).toHaveBeenCalledWith(DEFAULT_FILTER_VALUES);
    });

    it('calls sendSortApplyEvent with sort value when "Show packages" button is clicked', async () => {
      const user = userEvent.setup();
      const onSubmitMock = jest.fn();

      render(
        <TestHolidaysSortAndFilterModal
          isOpen
          selectedTab="filter"
          sortValue="price_asc"
          onSubmit={onSubmitMock}
        />,
        {
          brand: 'qantas',
          channel: 'holidays',
        },
      );

      await user.click(screen.getByRole('button', { name: 'Show packages' }));

      expect(mockSendSortApplyEvent).toHaveBeenCalledWith('price_asc');
    });

    it('calls onClearFilters when "Clear all" button is clicked', async () => {
      const user = userEvent.setup();
      const onClearFilters = jest.fn();

      render(
        <TestHolidaysSortAndFilterModal
          isOpen
          selectedTab="filter"
          onClearFilters={onClearFilters}
        />,
        {
          brand: 'qantas',
          channel: 'holidays',
        },
      );

      await user.click(screen.getByRole('button', { name: 'Clear all' }));

      expect(onClearFilters).toHaveBeenCalled();
    });
  });

  describe('Sort Tab', () => {
    it('shows the "Reset" and "Apply" CTA buttons', () => {
      render(<TestHolidaysSortAndFilterModal isOpen selectedTab="sort" />, {
        brand: 'qantas',
        channel: 'holidays',
      });

      expect(screen.getByRole('button', { name: 'Reset' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Apply' })).toBeInTheDocument();
    });

    it('calls onSubmit when "Apply" button is clicked', async () => {
      const user = userEvent.setup();
      const onSubmitMock = jest.fn();

      render(<TestHolidaysSortAndFilterModal isOpen selectedTab="sort" onSubmit={onSubmitMock} />, {
        brand: 'qantas',
        channel: 'holidays',
      });

      await user.click(screen.getByRole('button', { name: 'Apply' }));

      expect(onSubmitMock).toHaveBeenCalled();
    });

    it('calls sendSortApplyEvent with sortValue when "Apply" button is clicked', async () => {
      const user = userEvent.setup();
      const onSubmitMock = jest.fn();

      render(
        <TestHolidaysSortAndFilterModal
          isOpen
          selectedTab="sort"
          sortValue="price_asc"
          onSubmit={onSubmitMock}
        />,
        {
          brand: 'qantas',
          channel: 'holidays',
        },
      );

      await user.click(screen.getByRole('button', { name: 'Apply' }));

      expect(mockSendSortApplyEvent).toHaveBeenCalledWith('price_asc');
    });

    it('calls sendFilterApplyEvent with filterValues when "Apply" button is clicked', async () => {
      const user = userEvent.setup();

      render(
        <TestHolidaysSortAndFilterModal
          isOpen
          selectedTab="sort"
          filterValues={DEFAULT_FILTER_VALUES}
          onSubmit={jest.fn()}
        />,
        {
          brand: 'qantas',
          channel: 'holidays',
        },
      );

      await user.click(screen.getByRole('button', { name: 'Apply' }));

      expect(mockSendFilterApplyEvent).toHaveBeenCalledWith(DEFAULT_FILTER_VALUES);
    });

    it('calls onResetSort when "Reset" button is clicked', async () => {
      const user = userEvent.setup();
      const onClearSortMock = jest.fn();

      render(
        <TestHolidaysSortAndFilterModal isOpen selectedTab="sort" onClearSort={onClearSortMock} />,
        {
          brand: 'qantas',
          channel: 'holidays',
        },
      );

      await user.click(screen.getByRole('button', { name: 'Reset' }));

      expect(onClearSortMock).toHaveBeenCalled();
    });
  });

  describe('Modal Close Event', () => {
    it('calls sendModalCloseEvent with the current tab when modal is closed', async () => {
      const user = userEvent.setup();
      const onOpenChangeMock = jest.fn();

      render(
        <TestHolidaysSortAndFilterModal
          isOpen
          selectedTab="filter"
          onOpenChange={onOpenChangeMock}
        />,
        {
          brand: 'qantas',
          channel: 'holidays',
        },
      );

      await user.click(screen.getByRole('button', { name: 'Close' }));

      expect(mockSendModalCloseEvent).toHaveBeenCalledWith('filter');
      expect(onOpenChangeMock).toHaveBeenCalledWith(false);
    });

    it('calls sendModalCloseEvent with sort tab when modal is closed from sort tab', async () => {
      const user = userEvent.setup();
      const onOpenChangeMock = jest.fn();

      render(
        <TestHolidaysSortAndFilterModal
          isOpen
          selectedTab="sort"
          onOpenChange={onOpenChangeMock}
        />,
        {
          brand: 'qantas',
          channel: 'holidays',
        },
      );

      await user.click(screen.getByRole('button', { name: 'Close' }));

      expect(mockSendModalCloseEvent).toHaveBeenCalledWith('sort');
      expect(onOpenChangeMock).toHaveBeenCalledWith(false);
    });
  });
});
