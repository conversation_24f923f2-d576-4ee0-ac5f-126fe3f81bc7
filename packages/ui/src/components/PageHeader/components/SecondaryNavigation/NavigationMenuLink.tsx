import NextLink from 'next/link';
import { usePathname } from 'next/navigation';
import { Link as RadixLink } from '@radix-ui/react-navigation-menu';

import type { NavigationLink } from '../../../../types/navigation';

export const NavigationMenuLink = ({ href, ...props }: NavigationLink) => {
  const pathname = usePathname();
  const isActive = href === pathname;

  return (
    <RadixLink asChild active={isActive}>
      <NextLink prefetch={false} href={href} {...props} />
    </RadixLink>
  );
};
