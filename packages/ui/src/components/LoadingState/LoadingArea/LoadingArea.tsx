import type { JSX, ReactNode } from 'react';
import { useEffect } from 'react';

import { useSrAnnouncementsContext } from '../../../providers';
import { LoadingState } from '../components/LoadingState';

export interface LoadingAreaProps {
  /**
   * The loaded contents
   */
  children: ReactNode;
  isLoading: boolean;
  /**
   * Use `<LoadingArea.LoadingState>`
   */
  loadingState: ReactNode;
  /**
   * Name of the section being loaded (eg. the section heading)
   */
  sectionName: string;
  /**
   * Summary of loaded section. Keep this short and succinct
   * (eg. For search results - '3 results found' or 'No results found')
   */
  sectionSummary?: string;
}

/**
 * Use this for updating dynamic content within a page to announce status updates for screen reader users.
 *
 * This component populates the live region in `<SrAnnouncementsProvider>`.
 *
 * Use `<PageLoadingState>` for loading pages (ie. not dynamically loaded contents).
 */
export const LoadingArea = ({
  children,
  isLoading,
  loadingState,
  sectionName,
  sectionSummary,
  ...props
}: LoadingAreaProps): JSX.Element => {
  const { announceStatusUpdate } = useSrAnnouncementsContext();

  useEffect(() => {
    if (isLoading) {
      announceStatusUpdate(`Loading ${sectionName}`);
      return;
    }
    announceStatusUpdate(
      sectionSummary
        ? `Loading complete for ${sectionName}; ${sectionSummary}`
        : `Loading complete for ${sectionName}`,
    );
  }, [isLoading, announceStatusUpdate, sectionName, sectionSummary]);

  return (
    <div aria-busy={isLoading} {...props}>
      {isLoading ? loadingState : children}
    </div>
  );
};

LoadingArea.displayName = 'LoadingArea';

LoadingArea.LoadingState = LoadingState;
