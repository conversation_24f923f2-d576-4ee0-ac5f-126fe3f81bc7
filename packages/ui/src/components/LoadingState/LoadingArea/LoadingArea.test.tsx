import type { JSX } from 'react';
import { useEffect, useRef, useState } from 'react';

import { render, screen, userEvent } from '@qantasexperiences/ui/test-utils';

import type { LoadingAreaProps } from './LoadingArea';
import { SrAnnouncementsProvider } from '../../../providers';
import { LoadingArea } from './LoadingArea';

const LoadingAreaWrapper = ({ children, ...props }: Partial<LoadingAreaProps>): JSX.Element => (
  <SrAnnouncementsProvider>
    <LoadingArea
      sectionName="Properties"
      loadingState={<LoadingArea.LoadingState />}
      isLoading={false}
      {...props}
    >
      {children ?? 'Lorem ipsum'}
    </LoadingArea>
  </SrAnnouncementsProvider>
);

describe('<LoadingArea />', () => {
  it('shows loading state and sets accessible attributes when loading', () => {
    render(
      <LoadingAreaWrapper
        isLoading
        data-testid="test--loading-area"
        loadingState={<LoadingArea.LoadingState data-testid="test--loading-state" />}
      >
        Child content
      </LoadingAreaWrapper>,
    );
    expect(screen.getByTestId('test--loading-area')).toHaveAttribute('aria-busy', 'true');
    expect(screen.getByTestId('test--loading-state')).toBeVisible();
    expect(screen.queryByText('Child content')).not.toBeInTheDocument();
  });

  it('shows child content and sets accessible attributes when not loading', () => {
    render(
      <LoadingAreaWrapper
        isLoading={false}
        data-testid="test--loading-area"
        loadingState={<LoadingArea.LoadingState data-testid="test--loading-state" />}
      >
        Child content
      </LoadingAreaWrapper>,
    );
    expect(screen.getByTestId('test--loading-area')).toHaveAttribute('aria-busy', 'false');
    expect(screen.queryByTestId('test--loading-state')).not.toBeInTheDocument();
    expect(screen.queryByText('Child content')).toBeVisible();
  });

  it('populates the status region when loading', () => {
    render(<LoadingAreaWrapper isLoading={true} sectionName="Properties" />);
    expect(screen.getByRole('status').textContent).toBe('Loading Properties');
  });

  it('populates the status region when loading complete', () => {
    render(<LoadingAreaWrapper isLoading={false} sectionName="Payment form" />);
    expect(screen.getByRole('status').textContent).toBe('Loading complete for Payment form');
  });

  it('includes result summary in status when provided', () => {
    render(
      <LoadingAreaWrapper
        isLoading={false}
        sectionName="Properties"
        sectionSummary="3 results found"
      />,
    );
    expect(screen.getByRole('status').textContent).toBe(
      'Loading complete for Properties; 3 results found',
    );
  });

  it('updates the status to the latest update when multiple loading areas exist', async () => {
    const MultiLoadingArea = () => {
      const [firstIsLoading, setFirstIsLoading] = useState<boolean>(true);
      return (
        <SrAnnouncementsProvider>
          <button type="button" onClick={() => setFirstIsLoading(false)}>
            Show First results
          </button>
          <LoadingArea
            sectionName="First"
            loadingState={<LoadingArea.LoadingState />}
            isLoading={firstIsLoading}
            sectionSummary={firstIsLoading ? undefined : '3 results found'}
          >
            Contents
          </LoadingArea>
          <LoadingArea
            sectionName="Second"
            loadingState={<LoadingArea.LoadingState />}
            isLoading={true}
          >
            Contents
          </LoadingArea>
        </SrAnnouncementsProvider>
      );
    };

    const user = userEvent.setup();
    render(<MultiLoadingArea />);

    expect(screen.getByRole('status')).toHaveTextContent('Loading Second');

    await user.click(screen.getByRole('button', { name: 'Show First results' }));

    expect(screen.getByRole('status')).toHaveTextContent(
      'Loading complete for First; 3 results found',
    );
  });

  it('adds a delay for announcing update when request trigger has a focus return', async () => {
    const Wrapper = () => {
      const [searchStarted, setSearchStarted] = useState<boolean>(false);
      const [isLoading, setIsLoading] = useState<boolean>(false);
      const triggerRef = useRef<HTMLButtonElement>(null);

      useEffect(() => {
        if (searchStarted) setIsLoading(true);
      }, [searchStarted]);

      return (
        <SrAnnouncementsProvider>
          <button ref={triggerRef} type="button">
            Trigger
          </button>
          <dialog open>
            <button
              type="button"
              onClick={() => {
                setSearchStarted(true);
                triggerRef.current?.focus();
              }}
            >
              Search
            </button>
          </dialog>
          {searchStarted ? (
            <LoadingArea
              sectionName="Coffee"
              loadingState={<LoadingArea.LoadingState />}
              isLoading={isLoading}
            >
              Contents
            </LoadingArea>
          ) : (
            <p>Search not started - LoadingArea should not exist</p>
          )}
        </SrAnnouncementsProvider>
      );
    };

    const user = userEvent.setup();

    render(<Wrapper />);

    expect(screen.getByRole('status')).toBeEmptyDOMElement();

    await user.click(screen.getByRole('button', { name: 'Search' }));
    expect(screen.getByRole('button', { name: 'Trigger' })).toHaveFocus();

    expect(screen.getByRole('status')).toHaveTextContent('Loading Coffee');
  });
});
