{"name": "@qantasexperiences/ui", "private": false, "version": "0.22.0", "type": "module", "exports": {".": "./src/index.ts", "./Favicon": "./src/components/Favicon/index.ts", "./test-utils": "./src/test-utils/index.ts", "./assets/*": "./src/assets/*", "./providers": {"types": "./src/providers/index.server.ts", "react-server": "./src/providers/index.server.ts", "default": "./src/providers/index.ts"}}, "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint:css": "stylelint **/*.css --ignore-path ../../.gitignore --allow-empty-input", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "test:unit:watch": "jest --watch", "test:unit": "jest", "test:unit:ci": "jest", "typecheck": "tsc --noEmit"}, "dependencies": {"@contentful/rich-text-react-renderer": "^16.1.0", "@contentful/rich-text-types": "^17.1.0", "@hookform/resolvers": "^5.2.0", "@internationalized/date": "3.8.2", "@qantasexperiences/analytics": "workspace:*", "@qantasexperiences/auth": "workspace:*", "@qantasexperiences/data": "workspace:*", "@qantasexperiences/sanity": "workspace:*", "@qantasexperiences/tenants": "workspace:*", "@qantasexperiences/theming": "workspace:*", "@qantasexperiences/utils": "workspace:*", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-compose-refs": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@react-stately/utils": "^3.10.7", "date-fns": "^4.1.0", "embla-carousel": "^8.6.0", "embla-carousel-react": "^8.6.0", "fuse.js": "7.1.0", "lodash": "^4.17.21", "pluralize": "^8.0.0", "react-aria": "^3.41.1", "react-aria-components": "^1.10.1", "react-hook-form": "^7.61.1", "react-stately": "^3.39.0", "zod": "^3.25.67"}, "devDependencies": {"@next/third-parties": "^15.4.4", "@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/jest": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@qantasexperiences/utils": "workspace:*", "@react-types/calendar": "3.7.3", "@react-types/shared": "^3.31.0", "@storybook/addon-docs": "^9.0.18", "@storybook/react-vite": "^9.0.18", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/jest-axe": "^3.5.9", "@types/lodash": "^4.17.19", "@types/pluralize": "^0.0.33", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "jest": "^30.0.5", "jest-axe": "^10.0.0", "next": "^15.4.4", "react": "^19.1.0", "react-dom": "^19.1.0", "storybook": "^9.0.18", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}, "prettier": "@qantasexperiences/code-style/prettier", "peerDependencies": {"@next/third-parties": "^14.0 || ^15.0", "next": "^14.0 || ^15.0", "react": "^19.0"}, "beachball": {"shouldPublish": false}, "publishConfig": {"registry": "https://repositories.services.jqdev.net/repository/npm-local/"}}