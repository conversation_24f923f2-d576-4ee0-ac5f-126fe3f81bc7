/* eslint-disable jest/expect-expect -- reusing test suite across use cases */

import { removeUtmParams } from './url';

describe('removeUtmParams', () => {
  describe('removes all utm_* query parameters from the URL', () => {
    const mockUrl =
      'https://www.qantas.com/hotels/search/list?utm_source=merch&utm_medium=&utm_campaign=direct&featuredPropertyId=674278&location=Lyon%2C+France&adults=2&checkIn=2025-08-15&checkOut=2025-08-16';

    const testUrlString = (url: string) => {
      const urlObj = new URL(url);

      // Should not contain any utm_* params
      for (const key of urlObj.searchParams.keys()) {
        expect(key.startsWith('utm_')).toBe(false);
      }
      // Should retain other params
      expect(urlObj.searchParams.get('featuredPropertyId')).toBe('674278');
      expect(urlObj.searchParams.get('location')).toBe('Lyon, France');
      expect(urlObj.searchParams.get('adults')).toBe('2');
      expect(urlObj.searchParams.get('checkIn')).toBe('2025-08-15');
      expect(urlObj.searchParams.get('checkOut')).toBe('2025-08-16');
    };

    it('for URL objects', () => {
      testUrlString(removeUtmParams(new URL(mockUrl)));
    });

    it('for URL string', () => {
      testUrlString(removeUtmParams(mockUrl));
    });
  });

  describe('does nothing if there are no utm_* params', () => {
    const mockUrl =
      'https://www.qantas.com/hotels/search/list?featuredPropertyId=123&location=Sydney';

    const testUrlString = (url: string) => {
      const urlObj = new URL(url);
      expect(urlObj.searchParams.get('featuredPropertyId')).toBe('123');
      expect(urlObj.searchParams.get('location')).toBe('Sydney');
    };

    it('for URL objects', () => {
      testUrlString(removeUtmParams(new URL(mockUrl)));
    });

    it('for URL strings', () => {
      testUrlString(removeUtmParams(mockUrl));
    });
  });

  describe('handles URLs with only utm_* params', () => {
    const mockUrl = 'https://www.qantas.com/hotels/search/list?utm_source=merch&utm_medium=test';

    const testUrlString = (url: string) => {
      const urlObj = new URL(url);
      expect(Array.from(urlObj.searchParams.keys())).toHaveLength(0);
    };

    it('for URL objects', () => {
      testUrlString(removeUtmParams(new URL(mockUrl)));
    });

    it('for URL strings', () => {
      testUrlString(removeUtmParams(mockUrl));
    });
  });
});
