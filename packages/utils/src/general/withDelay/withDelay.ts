/**
 * Simple wrapper for setTimeout that executes an action using setTimeout if given a delay > 0,
 * otherwise immediately executes the action skipping setTimeout. This is as calling setTimeout with a
 * timeout of 0 still incurs some overhead and testing complexity.
 *
 * @param action - Function to be executed
 * @param delay - Amount to delay in milliseconds
 * @returns {void} - This function does not return a value.
 */
export const withDelay = (action: () => void, delay = 350) => {
  if (delay > 0) {
    setTimeout(() => {
      action();
    }, delay);
  } else {
    action();
  }
};
