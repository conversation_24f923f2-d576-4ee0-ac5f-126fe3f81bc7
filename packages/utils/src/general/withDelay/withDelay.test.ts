import { withDelay } from './withDelay';

describe('withDelay', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should call the action after the default 350ms delay', () => {
    const mockAction = jest.fn();

    withDelay(mockAction);

    expect(mockAction).not.toHaveBeenCalled();

    jest.advanceTimersByTime(349);

    expect(mockAction).not.toHaveBeenCalled();

    jest.advanceTimersByTime(1);

    expect(mockAction).toHaveBeenCalledTimes(1);
  });

  it('should call the action after the provided delay', () => {
    const mockAction = jest.fn();
    const customDelay = 1000;

    withDelay(mockAction, customDelay);

    expect(mockAction).not.toHaveBeenCalled();

    jest.advanceTimersByTime(customDelay - 1);

    expect(mockAction).not.toHaveBeenCalled();

    jest.advanceTimersByTime(1);

    expect(mockAction).toHaveBeenCalledTimes(1);
  });

  it('should call action immediately without calling setTimeout when delay is 0', () => {
    const mockAction = jest.fn();

    const setTimeoutSpy = jest.spyOn(global, 'setTimeout');

    withDelay(mockAction, 0);

    expect(mockAction).toHaveBeenCalledTimes(1);

    expect(setTimeoutSpy).not.toHaveBeenCalled();

    setTimeoutSpy.mockRestore();
  });
});
