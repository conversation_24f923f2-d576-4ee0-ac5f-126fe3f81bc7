{"name": "@qantasexperiences/utils", "private": false, "version": "0.7.0", "type": "module", "exports": {"./api": "./src/api/index.ts", "./browser": "./src/browser/index.ts", "./context": "./src/context/index.ts", "./date": "./src/date/index.ts", "./formatting": "./src/formatting/index.ts", "./general": "./src/general/index.ts", "./tailwind": "./src/tailwind/index.ts", "./zod": "./src/zod/index.ts", "./points": "./src/points/index.ts"}, "sideEffects": false, "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "typecheck": "tsc --noEmit", "test:unit": "jest", "test:unit:ci": "jest", "test:unit:watch": "jest --watch"}, "peerDependencies": {"react": "^19.0"}, "devDependencies": {"@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/jest": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^22.16.5", "@types/pluralize": "^0.0.33", "@types/react": "^19.1.8", "jest": "^30.0.5", "react": "^19.1.0", "typescript": "^5.8.3"}, "prettier": "@qantasexperiences/code-style/prettier", "dependencies": {"@date-fns/tz": "1.2.0", "@qantasexperiences/env": "workspace:*", "@qantasexperiences/tenants": "workspace:*", "camelcase-keys": "^9.1.3", "clsx": "^2.1.1", "date-fns": "^4.1.0", "pluralize": "^8.0.0", "server-only": "^0.0.1", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "zod": "^3.25.67"}, "beachball": {"shouldPublish": false}, "publishConfig": {"registry": "https://repositories.services.jqdev.net/repository/npm-local/"}}