{"name": "contentful-generate-destination-slug", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "preview": "vite preview", "create-app-definition": "contentful-app-scripts create-app-definition", "upload": "contentful-app-scripts upload --bundle-dir ./dist", "upload-ci": "contentful-app-scripts upload --ci --bundle-dir ./dist --organization-id $CONTENTFUL_ORG_ID --definition-id $CONTENTFUL_APP_DEF_ID --token $CONTENTFUL_ACCESS_TOKEN"}, "dependencies": {"@contentful/app-sdk": "^4.25.0", "@contentful/f36-components": "4.81.0", "@contentful/f36-tokens": "4.2.0", "@contentful/react-apps-toolkit": "1.2.16", "@emotion/css": "^11.11.2", "contentful-management": "11.54.3", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@contentful/app-scripts": "2.5.5", "@qantasexperiences/code-style": "workspace:*", "@types/node": "^22.16.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.3.2", "typescript": "^5.8.3", "vite": "^7.0.6"}, "prettier": "@qantasexperiences/code-style/prettier"}