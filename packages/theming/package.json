{"name": "@qantasexperiences/theming", "private": false, "version": "1.0.0", "type": "module", "exports": {"./global.css": "./src/styles/global.css", "./styles/*": "./src/styles/*", "./fonts": "./src/fonts/index.ts"}, "sideEffects": ["**/*.css"], "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint:css": "stylelint **/*.css --ignore-path ../../.gitignore --allow-empty-input", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "typecheck": "tsc --noEmit", "test:unit": "jest", "test:unit:ci": "jest", "test:unit:watch": "jest --watch"}, "dependencies": {"@qantasexperiences/tenants": "workspace:*"}, "devDependencies": {"@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/jest": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@tailwindcss/postcss": "^4.1.11", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/react": "^19.1.8", "jest": "^30.0.5", "next": "^15.4.4", "react": "^19.1.0", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "tailwindcss-border-image": "^1.1.2", "tailwindcss-react-aria-components": "^2.0.0", "typescript": "^5.8.3"}, "prettier": "@qantasexperiences/code-style/prettier", "peerDependencies": {"next": "^14.0 || ^15.0", "react": "^19.0"}, "beachball": {"shouldPublish": false}, "publishConfig": {"registry": "https://repositories.services.jqdev.net/repository/npm-local/"}}