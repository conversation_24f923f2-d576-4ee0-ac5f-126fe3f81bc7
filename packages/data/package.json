{"name": "@qantasexperiences/data", "private": false, "version": "0.6.0", "type": "module", "exports": {"./client": "./src/client/index.ts", "./handlers/*": "./src/handlers/*/index.ts", "./server": "./src/server/index.ts", "./schemas": "./src/schemas/index.ts", "./utils/*": "./src/utils/*/index.ts"}, "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "typecheck": "tsc --noEmit", "test:unit": "jest", "test:unit:ci": "jest --coverage", "test:unit:watch": "jest --watch"}, "dependencies": {"@qantasexperiences/auth": "workspace:*", "@qantasexperiences/env": "workspace:*", "@qantasexperiences/logger": "workspace:*", "@qantasexperiences/react-query": "workspace:*", "@qantasexperiences/sanity": "workspace:*", "@qantasexperiences/tenants": "workspace:*", "@qantasexperiences/utils": "workspace:*", "camelcase-keys": "^9.1.3", "date-fns": "^4.1.0", "ky": "1.7.5", "server-only": "^0.0.1", "uuid": "^11.1.0", "zod": "^3.25.67"}, "dependenciesComments": {"ky": "Version locked to 1.7.5 as 1.8.1 requires node v22"}, "peerDependencies": {"next": "^14.0 || ^15.0", "react": "^19.0"}, "devDependencies": {"@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/jest": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/react": "^19.1.8", "jest": "^30.0.5", "jest-fixed-jsdom": "^0.0.9", "msw": "^2.10.4", "next": "^15.4.4", "react": "^19.1.0", "typescript": "^5.8.3"}, "prettier": "@qantasexperiences/code-style/prettier", "beachball": {"shouldPublish": false}, "publishConfig": {"registry": "https://repositories.services.jqdev.net/repository/npm-local/"}}