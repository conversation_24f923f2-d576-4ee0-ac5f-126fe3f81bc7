{"name": "@qantasexperiences/env", "private": false, "version": "0.7.0", "type": "module", "exports": {"./client": "./src/client/index.ts", "./client/react": "./src/client/react/index.ts", "./schemas": "./src/schemas/index.ts", "./server": "./src/server/index.ts", "./utils": "./src/utils/index.ts", "./types": "./src/types.ts"}, "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "typecheck": "tsc --noEmit", "test:unit": "jest", "test:unit:ci": "jest", "test:unit:watch": "jest --watch"}, "dependencies": {"@qantasexperiences/tenants": "workspace:*", "zod": "^3.25.67"}, "peerDependencies": {"@t3-oss/env-nextjs": "^0.12.0", "next": "^14.0 || ^15.0", "react": "^19.0"}, "devDependencies": {"@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/jest": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@t3-oss/env-nextjs": "^0.13.8", "@testing-library/jest-dom": "^6.6.4", "@types/jest": "^30.0.0", "@types/node": "^22.16.5", "@types/react": "^19.1.8", "jest": "^30.0.5", "next": "^15.4.4", "react": "^19.1.0", "typescript": "^5.8.3"}, "prettier": "@qantasexperiences/code-style/prettier", "beachball": {"shouldPublish": false}, "publishConfig": {"registry": "https://repositories.services.jqdev.net/repository/npm-local/"}}