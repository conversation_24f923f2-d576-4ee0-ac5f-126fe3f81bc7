{"name": "@qantasexperiences/tenants", "private": false, "version": "0.4.0", "type": "module", "exports": {".": "./src/index.ts", "./react": "./src/react/index.ts", "./react/testing": "./src/react/testing/index.ts"}, "sideEffects": false, "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "typecheck": "tsc --noEmit", "test:unit": "jest", "test:unit:ci": "jest", "test:unit:watch": "jest --watch"}, "peerDependencies": {"react": "^19.0"}, "devDependencies": {"@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/jest": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^22.16.5", "@types/react": "^19.1.8", "jest": "^30.0.5", "react": "^19.1.0", "typescript": "^5.8.3"}, "prettier": "@qantasexperiences/code-style/prettier", "beachball": {"shouldPublish": false}, "publishConfig": {"registry": "https://repositories.services.jqdev.net/repository/npm-local/"}}