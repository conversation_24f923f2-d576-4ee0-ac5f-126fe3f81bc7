{"name": "@qantasexperiences/analytics", "entries": [{"date": "Wed, 13 Aug 2025 05:12:04 GMT", "version": "1.45.0", "tag": "@qantasexperiences/analytics_v1.45.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "894fb01dd7808488ff9703716f59b84cab9b94ca", "comment": "Revert \"chore: modify createHotelsOptimizelyFeatureDecisionEventPayload (#2017)\""}]}}, {"date": "<PERSON><PERSON>, 12 Aug 2025 14:09:51 GMT", "version": "1.44.0", "tag": "@qantasexperiences/analytics_v1.44.0", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "59c2011d3e5c8d8d5c1cd8046f2c66b7c6469372", "comment": "feat: add credit card and payment type analytics event creators and payloads"}]}}, {"date": "Tu<PERSON>, 12 Aug 2025 00:23:41 GMT", "version": "1.44.0", "tag": "@qantasexperiences/analytics_v1.44.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "44180bd8d2c45b39627f54e1406524eee7467dda", "comment": "chore: modify createHotelsOptimizelyFeatureDecisionEventPayload structure"}]}}, {"date": "Thu, 07 Aug 2025 02:39:27 GMT", "version": "1.43.0", "tag": "@qantasexperiences/analytics_v1.43.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "5daeb9403d3380b134ef2741e5b39052a849b7a8", "comment": "added tab click event payload creation"}]}}, {"date": "<PERSON><PERSON>, 29 Jul 2025 03:59:16 GMT", "version": "1.42.0", "tag": "@qantasexperiences/analytics_v1.42.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "73f4ba6287eae91c9511a210fc0692e17be48cb8", "comment": "login logout event payload creators"}]}}, {"date": "<PERSON><PERSON>, 29 Jul 2025 03:28:34 GMT", "version": "1.41.2", "tag": "@qantasexperiences/analytics_v1.41.2", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "fb905c07b7a27d3ceace70cd408cfc191a2f0117", "comment": "chore: upgrade @testing-library packages"}]}}, {"date": "Mon, 28 Jul 2025 00:21:49 GMT", "version": "1.41.1", "tag": "@qantasexperiences/analytics_v1.41.1", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "90b2fecffbce9bed2e4661149a391ab2dc9fa84d", "comment": "chore: update jest"}]}}, {"date": "Mon, 21 Jul 2025 00:52:53 GMT", "version": "1.41.0", "tag": "@qantasexperiences/analytics_v1.41.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "76e5426e00431bbcfb06daf57a1f142277aa086e", "comment": "feat: updated tests to include the actionType"}]}}, {"date": "Thu, 03 Jul 2025 04:23:08 GMT", "version": "1.40.0", "tag": "@qantasexperiences/analytics_v1.40.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "1a2790defa645246ae183bf6eb0eccf3dcd4d290", "comment": "feat: add strikethrough data to hotels analytic events"}]}}, {"date": "Wed, 02 Jul 2025 05:30:37 GMT", "version": "1.39.1", "tag": "@qantasexperiences/analytics_v1.39.1", "comments": {"patch": [{"author": "6095367+darab<PERSON>@users.noreply.github.com", "package": "@qantasexperiences/analytics", "commit": "b2aa6329b5949357610abf98a902baaec216d502", "comment": "feat: add view_search_results event (for holidays)"}]}}, {"date": "Wed, 02 Jul 2025 04:16:38 GMT", "version": "1.39.0", "tag": "@qantasexperiences/analytics_v1.39.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "da023998c1a190a8808c004e2a154e26027c2d20", "comment": "remove source field from createHotelsOptimizelyFeatureDecisionEventPayload"}]}}, {"date": "<PERSON><PERSON>, 01 Jul 2025 03:48:49 GMT", "version": "1.38.0", "tag": "@qantasexperiences/analytics_v1.38.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "3dbef6c80879e00943bc7088cb44b652b77fea80", "comment": "feat(ga4): add modal close event"}]}}, {"date": "Tue, 01 Jul 2025 00:55:55 GMT", "version": "1.37.0", "tag": "@qantasexperiences/analytics_v1.37.0", "comments": {"minor": [{"author": "6095367+darab<PERSON>@users.noreply.github.com", "package": "@qantasexperiences/analytics", "commit": "e179214f1ddda1a2a28f1ab2f68cab85f860bfba", "comment": "feat: add ga event for search_submit (holidays)"}]}}, {"date": "Tue, 01 Jul 2025 00:06:59 GMT", "version": "1.36.0", "tag": "@qantasexperiences/analytics_v1.36.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "b4695f026677a6ba08f2bb20f8a469bb02b598ed", "comment": "fix: fix typo - CLick to Click"}]}}, {"date": "Mon, 30 Jun 2025 02:09:53 GMT", "version": "1.35.1", "tag": "@qantasexperiences/analytics_v1.35.1", "comments": {"patch": [{"author": "6095367+darab<PERSON>@users.noreply.github.com", "package": "@qantasexperiences/analytics", "commit": "777185c84ca6a0375775fcafda0f8db25e999465", "comment": "feat: add sendHolidaysViewItemListEvent"}]}}, {"date": "Mon, 30 Jun 2025 00:44:11 GMT", "version": "1.35.0", "tag": "@qantasexperiences/analytics_v1.35.0", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "193362b355c7e522601f78bc9e4da699c15ad86d", "comment": "chore(deps): update jest and @types/lodash"}]}}, {"date": "Fri, 27 Jun 2025 07:45:44 GMT", "version": "1.35.0", "tag": "@qantasexperiences/analytics_v1.35.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "132ba5a52aed7674149ec11bc04577c1d025a305", "comment": "feat: added map_location_click enums"}]}}, {"date": "Fri, 27 Jun 2025 06:54:14 GMT", "version": "1.34.0", "tag": "@qantasexperiences/analytics_v1.34.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "ffdfe840c4c93e56d3b54f709a7f7c59a56a3266", "comment": "updated event creator name"}]}}, {"date": "Fri, 27 Jun 2025 05:45:43 GMT", "version": "1.33.0", "tag": "@qantasexperiences/analytics_v1.33.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "3a1d789fed451e4fe04c603287aee699f573e1a8", "comment": "feat: adding createTileClickEvent"}]}}, {"date": "Wed, 25 Jun 2025 07:30:54 GMT", "version": "1.32.0", "tag": "@qantasexperiences/analytics_v1.32.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "79316533da3425dce351c1c243c4052ff2d1e3e0", "comment": "feat(ga4): search zscalar error tracking ga4 event"}]}}, {"date": "Wed, 25 Jun 2025 05:36:54 GMT", "version": "1.31.0", "tag": "@qantasexperiences/analytics_v1.31.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "affba8c2ddeaa2fb9f676c67af1048701f91df6c", "comment": "chore: add createOptimizelyFeatureDecision and createHotelsOptimizelyFeatureDecisionEventPayload"}]}}, {"date": "Wed, 25 Jun 2025 04:43:32 GMT", "version": "1.30.0", "tag": "@qantasexperiences/analytics_v1.30.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "c727d781520b837caacc2a45d868d24b23d53e80", "comment": "feat: new data-helper for filter update event"}]}}, {"date": "Wed, 25 Jun 2025 03:45:36 GMT", "version": "1.29.1", "tag": "@qantasexperiences/analytics_v1.29.1", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "b0262035662606edaa5c3b8b92632d3d5a8f9fce", "comment": "Update Jest to v30"}]}}, {"date": "<PERSON><PERSON>, 24 Jun 2025 01:07:43 GMT", "version": "1.29.1", "tag": "@qantasexperiences/analytics_v1.29.1", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "c20daa2890949b8c13309f3a6b1682928aa20782", "comment": "chore(deps): update deps"}]}}, {"date": "Thu, 19 Jun 2025 07:22:08 GMT", "version": "1.29.0", "tag": "@qantasexperiences/analytics_v1.29.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "7374680821971f5c47a6b1bbdb95c6cda40c36c3", "comment": "cta click for search panel"}]}}, {"date": "<PERSON><PERSON>, 17 Jun 2025 03:55:15 GMT", "version": "1.28.0", "tag": "@qantasexperiences/analytics_v1.28.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "d18a8fb726fed19d0b78b94e0318fd11c7db8807", "comment": "fix: include travellers in events for analytics"}]}}, {"date": "Wed, 11 Jun 2025 02:00:08 GMT", "version": "1.27.0", "tag": "@qantasexperiences/analytics_v1.27.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "95b7021f61aadd7a7601621100bce1373bd95cae", "comment": "fix: omit available rooms data from other hotel events"}]}}, {"date": "<PERSON><PERSON>, 10 Jun 2025 06:53:05 GMT", "version": "1.26.1", "tag": "@qantasexperiences/analytics_v1.26.1", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "30cf5eb80b29ef51e1e78ac4461aea889edc2a27", "comment": "feat: add new event fields and refactor search error"}]}}, {"date": "<PERSON><PERSON>, 10 Jun 2025 06:04:57 GMT", "version": "1.26.0", "tag": "@qantasexperiences/analytics_v1.26.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "496346da3164ba79f65919f9f8eb25ae07ee9a91", "comment": "feat(ga4): initial commit"}]}}, {"date": "<PERSON><PERSON>, 03 Jun 2025 07:36:43 GMT", "version": "1.25.0", "tag": "@qantasexperiences/analytics_v1.25.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "2a82a9b60e76f12a227a6d0bae5b2717f7d59d61", "comment": "feat(ga4): search exit field analytics for desktop inputs implemented"}]}}, {"date": "Tue, 03 Jun 2025 06:59:30 GMT", "version": "1.24.0", "tag": "@qantasexperiences/analytics_v1.24.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "5134c192ae04a4045bc08adc7a67d0af541efb7a", "comment": "feat: wrap sort_filter, sort_open, sort_apply and filter_open events in event_data object"}]}}, {"date": "Mon, 02 Jun 2025 01:37:37 GMT", "version": "1.23.1", "tag": "@qantasexperiences/analytics_v1.23.1", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "727d79d0b4fae2269be5d9a9f596f94678b28d1d", "comment": "fix: ctaMessage position into test"}]}}, {"date": "Fri, 30 May 2025 05:34:34 GMT", "version": "1.23.0", "tag": "@qantasexperiences/analytics_v1.23.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "1afef0df017430ae478b4e50bc374f551d5517d7", "comment": "feat(ga4): created pagination events foundation"}]}}, {"date": "Fri, 30 May 2025 00:40:08 GMT", "version": "1.22.0", "tag": "@qantasexperiences/analytics_v1.22.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "0b31ab0b02d7b5ba43dccd0f8c54183d7ce4e0b6", "comment": "add form error event"}]}}, {"date": "Thu, 29 May 2025 02:48:22 GMT", "version": "1.21.0", "tag": "@qantasexperiences/analytics_v1.21.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "9c3242253a4627781b22d45f9230201d067bff22", "comment": "feat: create createHotelsViewItemListEventPayload"}]}}, {"date": "Thu, 29 May 2025 01:57:36 GMT", "version": "1.20.1", "tag": "@qantasexperiences/analytics_v1.20.1", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "618443d36a4514ab9baad6f1e9399e86534a42b0", "comment": "chore: update dev dependencies"}]}}, {"date": "Wed, 28 May 2025 07:52:37 GMT", "version": "1.20.0", "tag": "@qantasexperiences/analytics_v1.20.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "c98574af256441cf3474085f8258808543e3c6b8", "comment": "feat: update filter apply event with new data and return"}]}}, {"date": "Wed, 28 May 2025 05:37:37 GMT", "version": "1.19.1", "tag": "@qantasexperiences/analytics_v1.19.1", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "466043e0a1d32eba3d51bdffa8cfe0b402983248", "comment": "fix: update order of fields based on analytics feedback"}]}}, {"date": "Wed, 28 May 2025 04:15:56 GMT", "version": "1.19.0", "tag": "@qantasexperiences/analytics_v1.19.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "f9d9070b288671101fade5fe93cdea82b80ea43f", "comment": "feat: add form complete event"}]}}, {"date": "<PERSON><PERSON>, 27 May 2025 03:33:59 GMT", "version": "1.18.0", "tag": "@qantasexperiences/analytics_v1.18.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "ab497cd2828acabf6387b5d605717dd90a4a750b", "comment": "feat: add new search error event payload"}]}}, {"date": "Mon, 26 May 2025 04:42:48 GMT", "version": "1.17.0", "tag": "@qantasexperiences/analytics_v1.17.0", "comments": {"minor": [{"author": "annette.drap<PERSON><PERSON>@qantashotels.com", "package": "@qantasexperiences/analytics", "commit": "3df48ef4e2fc71708d288178ae1c770a09dd67f4", "comment": "feat: new ga4 analytics event login"}]}}, {"date": "Mon, 26 May 2025 03:56:02 GMT", "version": "1.16.0", "tag": "@qantasexperiences/analytics_v1.16.0", "comments": {"minor": [{"author": "annette.drap<PERSON><PERSON>@qantashotels.com", "package": "@qantasexperiences/analytics", "commit": "67e13660c5545017fe3afe9ab459a243ff7382e4", "comment": "feat: sendCtaClickEvent for destination pages"}]}}, {"date": "Mon, 26 May 2025 01:33:24 GMT", "version": "1.15.0", "tag": "@qantasexperiences/analytics_v1.15.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "cdfe7a3e303404e8540efb5afe43abf7b8aefcf8", "comment": "feat: add new view search results event and create hotels view search results event payload"}]}}, {"date": "Fri, 23 May 2025 06:58:54 GMT", "version": "1.14.0", "tag": "@qantasexperiences/analytics_v1.14.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "cb3a06d2b813255ac04e111b98a241c936cfbc7d", "comment": "feat: add modal show event"}]}}, {"date": "Fri, 23 May 2025 05:30:23 GMT", "version": "1.13.0", "tag": "@qantasexperiences/analytics_v1.13.0", "comments": {"minor": [{"author": "annette.drap<PERSON><PERSON>@qantashotels.com", "package": "@qantasexperiences/analytics", "commit": "4517fde708c4fbcc27128236aebbc0abead7a0fb", "comment": "feat: create ga4 v2 cta_click event"}]}}, {"date": "Thu, 22 May 2025 00:04:55 GMT", "version": "1.12.2", "tag": "@qantasexperiences/analytics_v1.12.2", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "7df0abc43ff4dbb37c2571ddaa9a948da6b63877", "comment": "chore(deps): bump the production-dependencies group with 15 updates"}]}}, {"date": "Mon, 19 May 2025 04:55:31 GMT", "version": "1.12.1", "tag": "@qantasexperiences/analytics_v1.12.1", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "1685ae68cf23373d8b08f1666815733497d4b73b", "comment": "fix: fix incorrect pay in points percent calculations"}]}}, {"date": "Mon, 19 May 2025 04:13:53 GMT", "version": "1.12.0", "tag": "@qantasexperiences/analytics_v1.12.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "a7f08a1808067cc2538a7a2e5932061680894e7a", "comment": "chore: add CreateHotelsSelectItemEvent"}]}}, {"date": "Mon, 19 May 2025 01:02:47 GMT", "version": "1.11.0", "tag": "@qantasexperiences/analytics_v1.11.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "9fc1a7e8b04a49ef972c14d113e946016aec67c6", "comment": "feat: add createUserPayload and createPageViewEventPayload data helpers"}]}}, {"date": "Fri, 16 May 2025 05:49:00 GMT", "version": "1.10.0", "tag": "@qantasexperiences/analytics_v1.10.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "b7bd170650811d97f40019884ee944c8a560aee7", "comment": "fix: refactor hotels begin checkout event"}]}}, {"date": "Fri, 16 May 2025 01:49:36 GMT", "version": "1.9.0", "tag": "@qantasexperiences/analytics_v1.9.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "a5446d937fd048eee92b034bb1127bad3a50712e", "comment": "added GTM event for footer link click"}]}}, {"date": "Fri, 16 May 2025 01:25:12 GMT", "version": "1.8.0", "tag": "@qantasexperiences/analytics_v1.8.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "ec1c962d1f7fcde078e1453bcb2a4bf765b6d63a", "comment": "feat: available rooms ga4 for purchase event"}]}}, {"date": "Thu, 15 May 2025 23:54:32 GMT", "version": "1.7.0", "tag": "@qantasexperiences/analytics_v1.7.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "f3783fa8efdd92cbb66ed0c5d39ee58ab2accaa3", "comment": "fix: refactor for null promotion in begin checkout event"}]}}, {"date": "Thu, 15 May 2025 09:45:19 GMT", "version": "1.6.0", "tag": "@qantasexperiences/analytics_v1.6.0", "comments": {"minor": [{"author": "annette.drap<PERSON><PERSON>@qantashotels.com", "package": "@qantasexperiences/analytics", "commit": "a93dec49b53d777d9011dcf17272dd3e3b7ae7f9", "comment": "feat: new ga4 v2 event view item"}]}}, {"date": "Thu, 15 May 2025 05:05:28 GMT", "version": "1.5.3", "tag": "@qantasexperiences/analytics_v1.5.3", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "451534be421596c3b3162e76be5e5d4c91ace2a1", "comment": "chore(deps-dev): bump the development-dependencies group across 1 directory with 5 updates"}]}}, {"date": "Wed, 14 May 2025 06:23:00 GMT", "version": "1.5.2", "tag": "@qantasexperiences/analytics_v1.5.2", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "11583b1b5aac794e533607381749fca18315236c", "comment": "add jest-fail-on-console to fail tests with warnings"}]}}, {"date": "Wed, 14 May 2025 01:29:10 GMT", "version": "1.5.2", "tag": "@qantasexperiences/analytics_v1.5.2", "comments": {"patch": [{"author": "annette.drap<PERSON><PERSON>@qantashotels.com", "package": "@qantasexperiences/analytics", "commit": "5ab494862381687f2a7bdc61edd2397ccd40547f", "comment": "feat: promotion could be null"}]}}, {"date": "<PERSON><PERSON>, 13 May 2025 23:59:33 GMT", "version": "1.5.1", "tag": "@qantasexperiences/analytics_v1.5.1", "comments": {"patch": [{"author": "annette.drap<PERSON><PERSON>@qantashotels.com", "package": "@qantasexperiences/analytics", "commit": "74b6cfce52158d80e66038fd4470c74bf19a8d25", "comment": "feat: refine hasOffer condition"}]}}, {"date": "<PERSON><PERSON>, 13 May 2025 06:42:50 GMT", "version": "1.5.0", "tag": "@qantasexperiences/analytics_v1.5.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "94fe01d4edc03ab9214e52fdf792326a73b1436c", "comment": "feat: add data helper createHotelsBeginCheckoutEventPayload"}]}}, {"date": "<PERSON><PERSON>, 13 May 2025 06:04:52 GMT", "version": "1.4.0", "tag": "@qantasexperiences/analytics_v1.4.0", "comments": {"minor": [{"author": "annette.drap<PERSON><PERSON>@qantashotels.com", "package": "@qantasexperiences/analytics", "commit": "f49d000a81e68749f4cae70233986246e94ce2cb", "comment": "feat: export createHotelsAddToCartEvent"}]}}, {"date": "<PERSON><PERSON>, 13 May 2025 04:37:58 GMT", "version": "1.3.0", "tag": "@qantasexperiences/analytics_v1.3.0", "comments": {"minor": [{"author": "annette.drap<PERSON><PERSON>@qantashotels.com", "package": "@qantasexperiences/analytics", "commit": "17607f07e0a0d95a77347041987a71d7fac55229", "comment": "feat: add new event-creator and data-helper pattern"}]}}, {"date": "<PERSON><PERSON>, 13 May 2025 01:23:12 GMT", "version": "1.2.0", "tag": "@qantasexperiences/analytics_v1.2.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "1824b172871fd406e9f2fcc87fb84a8ecd2bcfb8", "comment": "feat: add filter open, select and apply events and data-helpers"}], "none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "4e5b8467fd3aa75be07764923063c747b9958cd2", "comment": "feat: add tooltip to component-type enum"}]}}, {"date": "Thu, 08 May 2025 04:37:56 GMT", "version": "1.1.4", "tag": "@qantasexperiences/analytics_v1.1.4", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "55336d77a34cff13cacd5c933bc8b073da1ae493", "comment": "Update pm2 to ^6.0.5"}]}}, {"date": "Wed, 07 May 2025 05:16:29 GMT", "version": "1.1.4", "tag": "@qantasexperiences/analytics_v1.1.4", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "865c3bc57e4098073e1a06dc3aed373c9224a808", "comment": "Dependency updates. No expected changes."}]}}, {"date": "Wed, 07 May 2025 02:05:01 GMT", "version": "1.1.3", "tag": "@qantasexperiences/analytics_v1.1.3", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "08095fd2f0feda46523a2be08c1062a056942e33", "comment": "Dep updates. No change expected."}]}}, {"date": "Fri, 02 May 2025 00:02:11 GMT", "version": "1.1.2", "tag": "@qantasexperiences/analytics_v1.1.2", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "5a442c8c67c142e81fd6e8051c9218975fddf3d2", "comment": "feat: add new event-creator and data-helper pattern"}]}}, {"date": "<PERSON><PERSON>, 29 Apr 2025 06:44:13 GMT", "version": "1.1.1", "tag": "@qantasexperiences/analytics_v1.1.1", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "d192b726abaee400f86d2ed8195d7c0d28802a3c", "comment": "feat: add destination to event_data"}]}}, {"date": "<PERSON><PERSON>, 29 Apr 2025 00:13:46 GMT", "version": "1.1.0", "tag": "@qantasexperiences/analytics_v1.1.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "feaa469b1810dbf05d7040be7b59a434d73780e1", "comment": "feat: add select_item GTM event"}]}}, {"date": "Wed, 23 Apr 2025 01:20:07 GMT", "version": "1.0.0", "tag": "@qantasexperiences/analytics_v1.0.0", "comments": {"major": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "ffa70202422252822ef92493a6aa7962fc3db6bd", "comment": "refactor(analytics): remove createEvent abstraction and export events directly"}]}}, {"date": "Fri, 11 Apr 2025 00:53:36 GMT", "version": "0.20.0", "tag": "@qantasexperiences/analytics_v0.20.0", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "4e7efda09dfd168803755e3dbded13203925cbfa", "comment": "Linting type imports"}]}}, {"date": "Thu, 03 Apr 2025 04:06:58 GMT", "version": "0.20.0", "tag": "@qantasexperiences/analytics_v0.20.0", "comments": {"minor": [{"author": "annette.drap<PERSON><PERSON>@qantashotels.com", "package": "@qantasexperiences/analytics", "commit": "09b513822e10f88e8b2cec7eb4c2265f1a95ed61", "comment": "feat: add missing field page_type to the cta click event"}]}}, {"date": "Wed, 02 Apr 2025 06:57:46 GMT", "version": "0.19.0", "tag": "@qantasexperiences/analytics_v0.19.0", "comments": {"minor": [{"author": "annette.drap<PERSON><PERSON>@qantashotels.com", "package": "@qantasexperiences/analytics", "commit": "eae0b9fd8d7e50ec75a6c606b20bc8a0bcae6cdd", "comment": "feat: additional fields required for the tab click GTM event"}]}}, {"date": "Fri, 28 Mar 2025 00:47:55 GMT", "version": "0.18.0", "tag": "@qantasexperiences/analytics_v0.18.0", "comments": {"minor": [{"author": "annette.drap<PERSON><PERSON>@qantashotels.com", "package": "@qantasexperiences/analytics", "commit": "895a7bd81ae0790b1561f87d318033491172b66d", "comment": "feat: add GTM Event to QuickLinks"}]}}, {"date": "Fri, 28 Mar 2025 00:16:03 GMT", "version": "0.17.0", "tag": "@qantasexperiences/analytics_v0.17.0", "comments": {"minor": [{"author": "annette.drap<PERSON><PERSON>@qantashotels.com", "package": "@qantasexperiences/analytics", "commit": "171b2c995a3834ff20f8eba10bd4aabb33f5f55f", "comment": "feat: add new GTM event type tab-click"}]}}, {"date": "Thu, 20 Mar 2025 05:30:05 GMT", "version": "0.16.0", "tag": "@qantasexperiences/analytics_v0.16.0", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "d1ea9eecbf27092a54dc4510408ec36b3a23a5a7", "comment": "feat(ga4): custom click event for login"}]}}, {"date": "Wed, 12 Mar 2025 22:08:21 GMT", "version": "0.16.0", "tag": "@qantasexperiences/analytics_v0.16.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "6d75b5e2768223fd52e9f0008c2ad04809b058fb", "comment": "chore: create search-field-exit event"}]}}, {"date": "Wed, 12 Mar 2025 04:41:24 GMT", "version": "0.15.0", "tag": "@qantasexperiences/analytics_v0.15.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "48e1ae300d8a077db6fbf17f3b6109f84cf03227", "comment": "feat: update enums for relationship page view events and export schemas"}]}}, {"date": "Wed, 12 Mar 2025 02:39:03 GMT", "version": "0.14.0", "tag": "@qantasexperiences/analytics_v0.14.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "6bb837abe02c602a29e70f21e376d0564e9fc8db", "comment": "add cta-link event"}]}}, {"date": "Wed, 05 Mar 2025 05:24:39 GMT", "version": "0.13.1", "tag": "@qantasexperiences/analytics_v0.13.1", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "64e98f12b1cc7dbbe139f52029c14b5a14cb7dda", "comment": "chore: lint error"}]}}, {"date": "Wed, 05 Mar 2025 02:17:26 GMT", "version": "0.13.0", "tag": "@qantasexperiences/analytics_v0.13.0", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "40cd6fe67277109c5a95d566c37334c1fef3bcfc", "comment": "chore: bump packages"}, {"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "d3b21b0bd89b09beac26a3d989a98379c7e61fbc", "comment": "chore: upgrade dependencies"}], "minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "d0ea34658c7e0bb92b26c4f141c1b5894dcbc55f", "comment": "chore: bump version"}, {"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "8bb2aaddd094c69662da5e60c9af7a3c169841d4", "comment": "Adding search submit event when users click on the search button from the activities page"}]}}, {"date": "Mon, 03 Feb 2025 05:32:37 GMT", "version": "0.9.3", "tag": "@qantasexperiences/analytics_v0.9.3", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "8c05edbae09bc0f9031fe3c1cd24765b9b6a4569", "comment": "chore: version bump analytics package"}, {"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "9a471a41be2a2910c8a8441c7ed7be32ed066360", "comment": "chore(deps): bump the production-dependencies group across 1 directory with 15 updates"}]}}, {"date": "<PERSON><PERSON>, 28 Jan 2025 03:45:25 GMT", "version": "0.9.0", "tag": "@qantasexperiences/analytics_v0.9.0", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "0e5b748f8d7a621dea7670ca9b4fdc5fc7fabf92", "comment": "Eslint v9 upgrade. This should not have any impact on consumers, but releasing a patch as source code has been updated with linting fixes."}], "minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "a0a34fde139cc147ff5abc2b1039e6b9b47e7a2d", "comment": "feat: publish all required packages"}]}}, {"date": "Fri, 10 Jan 2025 03:05:54 GMT", "version": "0.8.1", "tag": "@qantasexperiences/analytics_v0.8.1", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "6d05b5b9b45e7993068117906e8d4511e027ca6e", "comment": "chore: update package.json"}]}}, {"date": "Wed, 11 Dec 2024 07:04:50 GMT", "version": "0.8.1", "tag": "@qantasexperiences/analytics_v0.8.1", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "f863aa3c96a32370c735aec90e03b0acc9ebf9af", "comment": "chore(deps-dev): bump the development-dependencies group with 27 updates"}]}}, {"date": "Tue, 03 Dec 2024 06:03:59 GMT", "version": "0.8.0", "tag": "@qantasexperiences/analytics_v0.8.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "360d5a17e3e456775a022f70e039f78690d3269c", "comment": "feat: add deposit pay attributes to purchase event"}]}}, {"date": "Wed, 27 Nov 2024 23:55:20 GMT", "version": "0.7.3", "tag": "@qantasexperiences/analytics_v0.7.3", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "f10ad5911f994bdf82dc5230fd5531ead659b6ae", "comment": "fixing linting warnings"}]}}, {"date": "Sun, 17 Nov 2024 23:02:53 GMT", "version": "0.7.2", "tag": "@qantasexperiences/analytics_v0.7.2", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "37a5a40f3d0998c97644f4e381ecf5815e6a6520", "comment": "chore(deps-dev): bump the development-dependencies group with 19 updates"}]}}, {"date": "Wed, 13 Nov 2024 04:56:22 GMT", "version": "0.7.1", "tag": "@qantasexperiences/analytics_v0.7.1", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "edb3fc2f51d5a101405bcf6cb5390ec479d4c47e", "comment": "chore(deps-dev): bump the development-dependencies group across 1 directory with 29 updates"}]}}, {"date": "Wed, 13 Nov 2024 01:29:41 GMT", "version": "0.7.0", "tag": "@qantasexperiences/analytics_v0.7.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "8fc39335a01edc7c729d0c51c0d119c9bfcbcb1e", "comment": "noUncheckedIndexedAccess: true"}]}}, {"date": "<PERSON><PERSON>, 12 Nov 2024 06:49:25 GMT", "version": "0.6.0", "tag": "@qantasexperiences/analytics_v0.6.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "c36c804023efdeea6f2a9110321f605b46d63951", "comment": "disable no-unnecessary-condition with FIXMEs"}]}}, {"date": "Thu, 31 Oct 2024 00:44:10 GMT", "version": "0.5.3", "tag": "@qantasexperiences/analytics_v0.5.3", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "555d375d974fdf62ba948e202d1237120c935389", "comment": "chore(deps-dev): bump the development-dependencies group across 1 directory with 10 updates"}]}}, {"date": "<PERSON><PERSON>, 22 Oct 2024 23:52:34 GMT", "version": "0.5.2", "tag": "@qantasexperiences/analytics_v0.5.2", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "d6a117e1e3021e9e0cc19854cc24ec8f2742a166", "comment": "chore: consolidate Jest config with node and jsdom, use file extensions to select env"}]}}, {"date": "Thu, 17 Oct 2024 06:11:36 GMT", "version": "0.5.2", "tag": "@qantasexperiences/analytics_v0.5.2", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "47d9a28828e10e41408bff43bf7c94884c8efcba", "comment": "more eslint rules"}]}}, {"date": "Wed, 16 Oct 2024 23:49:31 GMT", "version": "0.5.2", "tag": "@qantasexperiences/analytics_v0.5.2", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "4a11a5c6583a094d317ddd50abf3eea1d6895880", "comment": "object-shorthand"}]}}, {"date": "Wed, 16 Oct 2024 05:09:41 GMT", "version": "0.5.2", "tag": "@qantasexperiences/analytics_v0.5.2", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "8784d4dcea1e64f9322ea1394340d26a7d0b5c18", "comment": "Introduce PM2 to manage the development process and prepare for future build environment injection"}]}}, {"date": "<PERSON><PERSON>, 15 Oct 2024 04:51:44 GMT", "version": "0.5.2", "tag": "@qantasexperiences/analytics_v0.5.2", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "90b9993eafb3f69bdc8f19d9e7776bbc20da3703", "comment": "chore(deps-dev): bump the development-dependencies group across 1 directory with 7 updates"}]}}, {"date": "Mon, 14 Oct 2024 23:33:40 GMT", "version": "0.5.1", "tag": "@qantasexperiences/analytics_v0.5.1", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "58b55ed6be64413fbb1e6f5f9c045254d3a1d954", "comment": "chore(deps-dev): bump contentful deps"}]}}, {"date": "Mon, 14 Oct 2024 00:43:27 GMT", "version": "0.5.0", "tag": "@qantasexperiences/analytics_v0.5.0", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "030eeeb91f54ca96c6c25719d27397da63ecf990", "comment": "create code-style"}]}}, {"date": "Thu, 10 Oct 2024 22:14:20 GMT", "version": "0.5.0", "tag": "@qantasexperiences/analytics_v0.5.0", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "b4fdadf7c3937082cf155cc04ab943ec28f79e03", "comment": "update format:check script to includes all files"}]}}, {"date": "Thu, 10 Oct 2024 05:33:04 GMT", "version": "0.5.0", "tag": "@qantasexperiences/analytics_v0.5.0", "comments": {"none": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "1566be480e60d14d765ef08557a14c2b2d7d59f7", "comment": "eslint fix"}]}}, {"date": "Wed, 09 Oct 2024 22:30:54 GMT", "version": "0.5.0", "tag": "@qantasexperiences/analytics_v0.5.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "6156759047b48554499cc5d96b1145e617c0d41f", "comment": "chore(deps-dev): upgrade typescript"}]}}, {"date": "Wed, 09 Oct 2024 04:39:54 GMT", "version": "0.4.2", "tag": "@qantasexperiences/analytics_v0.4.2", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "73e8819641c5db7d63d805d19997e76efe8786fa", "comment": "chore(deps-dev): bump non-group dependencies"}]}}, {"date": "Wed, 25 Sep 2024 01:06:43 GMT", "version": "0.4.1", "tag": "@qantasexperiences/analytics_v0.4.1", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "edda165528ed3feae7624119fdeccc4cf8fe7733", "comment": "fix: build package before release"}]}}, {"date": "<PERSON><PERSON>, 24 Sep 2024 05:47:02 GMT", "version": "0.4.0", "tag": "@qantasexperiences/analytics_v0.4.0", "comments": {"minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "7fde75f917c2a7af750ce86b2a3ba0638167dfdb", "comment": "chore: change groupName and itemName for Filter Update to reflect UI naming change."}]}}, {"date": "<PERSON><PERSON>, 17 Sep 2024 01:37:39 GMT", "version": "0.3.1", "tag": "@qantasexperiences/analytics_v0.3.1", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "6faf42ff2e134d382f02952e0f2b1a302772a622", "comment": "fix: pipeline"}]}}, {"date": "Mon, 16 Sep 2024 23:36:04 GMT", "version": "0.3.0", "tag": "@qantasexperiences/analytics_v0.3.0", "comments": {"patch": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "af7533f5a07426e2ee9c7777bda70c5a78a79aaf", "comment": "fix: pay at property value for purchase event"}, {"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "ab5e13c5224c745556e9e0cab25ee44dfd124901", "comment": "feat: automate releases of analytics"}, {"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "085ab5a368121fb11e9d4e75ab1e07e67d13e9ee", "comment": "fix schema to include Deals Page filters"}], "minor": [{"author": "<EMAIL>", "package": "@qantasexperiences/analytics", "commit": "b0a25b0dcf7d16d475371e6fa7a396f87cf2e192", "comment": "chore: remove no more necessary search_start event and add the new search_suggestion_display"}]}}]}