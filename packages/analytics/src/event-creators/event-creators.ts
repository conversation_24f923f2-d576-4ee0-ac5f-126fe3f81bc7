import { EventType } from '../enums';
import { createEventCreator } from './createEventCreator';

export const createAddPaymentInfoEvent = createEventCreator(EventType.ADD_PAYMENT_INFO);
export const createAddToCartEvent = createEventCreator(EventType.ADD_TO_CART);
export const createBeginCheckoutEvent = createEventCreator(EventType.BEGIN_CHECKOUT);
export const createCheckoutErrorEvent = createEventCreator(EventType.CHECKOUT_ERROR);
export const createCtaClickEvent = createEventCreator(EventType.CTA_CLICK);
export const createCustomClickEvent = createEventCreator(EventType.CUSTOM_CLICK);
export const createFilterApplyEvent = createEventCreator(EventType.FILTER_APPLY);
export const createFilterOpenEvent = createEventCreator(EventType.FILTER_OPEN);
export const createFilterSelectEvent = createEventCreator(EventType.FILTER_SELECT);
export const createFilterUpdateEvent = createEventCreator(EventType.FILTER_UPDATE);
export const createFormErrorEvent = createEventCreator(EventType.FORM_ERROR);
export const createFooterClickEvent = createEventCreator(EventType.FOOTER_CLICK);
export const createFormCompleteEvent = createEventCreator(EventType.FORM_COMPLETE);
export const createLoginEvent = createEventCreator(EventType.LOGIN);
export const createLogoutEvent = createEventCreator(EventType.LOGOUT);
export const createMenuClickEvent = createEventCreator(EventType.MENU_CLICK);
export const createModalShowEvent = createEventCreator(EventType.MODAL_SHOW);
export const createModalCloseEvent = createEventCreator(EventType.MODAL_CLOSE);
export const createPageViewEvent = createEventCreator(EventType.PAGE_VIEW);
export const createPromotionCodeApplyEvent = createEventCreator(EventType.PROMOTION_CODE_APPLY);
export const createPurchaseEvent = createEventCreator(EventType.PURCHASE);
export const createSearchExitFieldEvent = createEventCreator(EventType.SEARCH_EXIT_FIELD);
export const createSearchSubmitEvent = createEventCreator(EventType.SEARCH_SUBMIT);
export const createSearchSuggestionClickEvent = createEventCreator(
  EventType.SEARCH_SUGGESTION_CLICK,
);
export const createSearchSuggestionDisplayEvent = createEventCreator(
  EventType.SEARCH_SUGGESTION_DISPLAY,
);
export const createSelectItemEvent = createEventCreator(EventType.SELECT_ITEM);
export const createSelectPromotionEvent = createEventCreator(EventType.SELECT_PROMOTION);
export const createSortApplyEvent = createEventCreator(EventType.SORT_APPLY);
export const createSortOpenEvent = createEventCreator(EventType.SORT_OPEN);
export const createSortSelectEvent = createEventCreator(EventType.SORT_SELECT);
export const createTabClickEvent = createEventCreator(EventType.TAB_CLICK);
export const createTileClickEvent = createEventCreator(EventType.TILE_CLICK);
export const createViewItemEvent = createEventCreator(EventType.VIEW_ITEM);
export const createViewItemListEvent = createEventCreator(EventType.VIEW_ITEM_LIST);
export const createViewPromotionEvent = createEventCreator(EventType.VIEW_PROMOTION);
export const createSearchErrorEvent = createEventCreator(EventType.SEARCH_ERROR);
export const createViewSearchResultsEvent = createEventCreator(EventType.VIEW_SEARCH_RESULTS);
export const createPaginationClickEvent = createEventCreator(EventType.PAGINATION_CLICK);
export const createMapLocationClickEvent = createEventCreator(EventType.MAP_LOCATION_CLICK);
export const createMapOpenClickEvent = createEventCreator(EventType.MAP_OPEN);
export const createOptimizelyFeatureDecisionEvent = createEventCreator(
  EventType.OPTIMIZELY_FEATURE_DECISION,
);
export const createCreditCardAddEvent = createEventCreator(EventType.CREDIT_CARD_ADD);
export const createCreditCardApplyEvent = createEventCreator(EventType.CREDIT_CARD_APPLY);
export const createCreditCardSelectEvent = createEventCreator(EventType.CREDIT_CARD_SELECT);
export const createPaymentTypeSelectEvent = createEventCreator(EventType.PAYMENT_TYPE_SELECT);
