import { ActionType } from '../../enums';
import { createLoginEventPayload } from './createLoginEventPayload';

describe('createLoginEventPayload', () => {
  const basePayload = {
    isLoggedIn: true,
    userId: '12345',
    userPoints: 100,
    userQffHash: 'abcde',
  };

  it('creates a valid login payload when all fields are populated', () => {
    const result = createLoginEventPayload(basePayload);
    expect(result).toStrictEqual({
      event_data: {
        action: ActionType.LOGIN,
        method: 'hard',
      },
      user: {
        user_id: '12345',
        user_login_status: 'logged in',
        user_points: 100,
        user_qff_hash: 'abcde',
      },
    });
  });

  it('includes pageType when provided', () => {
    const payloadWithPageType = {
      ...basePayload,
      pageType: 'search',
    };
    const result = createLoginEventPayload(payloadWithPageType);
    expect(result.event_data.page_type).toBe('search');
  });

  it('surfaces proper login method when provided', () => {
    const payloadWithLoginMethod = {
      ...basePayload,
      method: 'soft',
    } as const;
    const result = createLoginEventPayload(payloadWithLoginMethod);
    expect(result.event_data.method).toBe('soft');
  });
});
