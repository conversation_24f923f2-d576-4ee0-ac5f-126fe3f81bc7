import { ActionType } from '../../enums';
import { createUserPayload } from '../createUserPayload';

type LoginMethod = 'hard' | 'soft';
export interface CreateLoginEventPayloadOptions {
  isLoggedIn: boolean;
  method?: LoginMethod;
  pageType?: string;
  userId: string;
  userPoints: number;
  userQffHash: string;
}
export interface CreateLoginEventPayloadReturn {
  event_data: {
    action: ActionType;
    method: LoginMethod;
    page_type?: string;
  };
  user: ReturnType<typeof createUserPayload>;
}
export const createLoginEventPayload = ({
  pageType,
  isLoggedIn,
  userId,
  userPoints,
  userQffHash,
  method = 'hard',
}: CreateLoginEventPayloadOptions): CreateLoginEventPayloadReturn => {
  const payload: CreateLoginEventPayloadReturn = {
    event_data: {
      action: ActionType.LOGIN,
      method,
    },
    user: createUserPayload({
      userId,
      qffHashId: userQffHash,
      isLoggedIn,
      qffPoints: userPoints,
    }),
  };

  if (pageType !== undefined) {
    payload.event_data.page_type = pageType;
  }

  return payload;
};
