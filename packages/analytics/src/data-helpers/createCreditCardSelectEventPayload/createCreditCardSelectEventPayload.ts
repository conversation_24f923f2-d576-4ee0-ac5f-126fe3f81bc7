import { ActionType, ComponentType } from '../../enums';

export interface CreateCreditCardSelectEventPayloadOptions {
  itemId: string;
  itemName: string;
  itemType: string;
  provider: string;
}

export interface CreateCreditCardSelectEventPayloadReturn {
  event_data: {
    action: ActionType.SELECT;
    component_type: ComponentType.CARD;
    item_id: string;
    item_name: string;
    item_type: string;
    provider: string;
  };
}

export const createCreditCardSelectEventPayload = ({
  itemId,
  itemName,
  itemType,
  provider,
}: CreateCreditCardSelectEventPayloadOptions): CreateCreditCardSelectEventPayloadReturn => {
  return {
    event_data: {
      action: ActionType.SELECT,
      component_type: ComponentType.CARD,
      item_id: itemId,
      item_name: itemName,
      item_type: itemType,
      provider,
    },
  };
};
