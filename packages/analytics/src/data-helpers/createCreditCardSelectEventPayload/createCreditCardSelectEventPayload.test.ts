import { ActionType, ComponentType } from '../../enums';
import { createCreditCardSelectEventPayload } from './createCreditCardSelectEventPayload';

describe('createCreditCardSelectEventPayload', () => {
  it('creates a valid credit_card_select event payload', () => {
    const result = createCreditCardSelectEventPayload({
      itemId: '123',
      itemName: 'Credit Card',
      itemType: 'credit_card',
      provider: 'provider',
    });

    expect(result).toStrictEqual({
      event_data: {
        action: ActionType.SELECT,
        component_type: ComponentType.CARD,
        item_id: '123',
        item_name: 'Credit Card',
        item_type: 'credit_card',
        provider: 'provider',
      },
    });
  });
});
