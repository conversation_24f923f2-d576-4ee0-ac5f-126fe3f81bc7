/**
 * @jest-environment jsdom
 */

import { ComponentType } from '../../enums';
import { createModalCloseEventPayload } from './createModalCloseEventPayload';

Object.defineProperty(window, 'location', {
  value: {
    href: 'https://localhost:8000/qantas/holidays/search/list',
  },
  writable: true,
});

describe('createModalCloseEventPayload', () => {
  it('creates a valid modal close event payload', () => {
    const result = createModalCloseEventPayload({
      componentType: ComponentType.FILTER_SORT_MODAL,
      componentVariant: 'sort',
      pageType: 'search',
    });

    expect(result).toStrictEqual({
      event_data: {
        action: 'close',
        component_type: 'filter_sort_modal',
        component_variant: 'sort',
        page_type: 'search',
        item_type: 'button',
        url: 'https://localhost:8000/qantas/holidays/search/list',
      },
    });
  });
});
