import { ActionType, ComponentType } from '../../enums';
import { createCreditCardApplyEventPayload } from './createCreditCardApplyEventPayload';

describe('createCreditCardApplyEventPayload', () => {
  it('creates a valid credit_card_apply event payload', () => {
    const result = createCreditCardApplyEventPayload({
      customData: 'custom_data',
      itemId: '123',
      itemName: 'Credit Card',
      itemType: 'credit_card',
      provider: 'provider',
    });

    expect(result).toStrictEqual({
      event_data: {
        action: ActionType.APPLY,
        component_type: ComponentType.CARD,
        custom_data: 'custom_data',
        item_id: '123',
        item_name: 'Credit Card',
        item_type: 'credit_card',
        provider: 'provider',
      },
    });
  });
});
