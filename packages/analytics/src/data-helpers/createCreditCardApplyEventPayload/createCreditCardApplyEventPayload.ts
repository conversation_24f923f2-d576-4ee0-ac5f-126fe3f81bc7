import { ActionType, ComponentType } from '../../enums';

export interface CreateCreditCardApplyEventPayloadOptions {
  customData: string;
  itemId: string;
  itemName: string;
  itemType: string;
  provider: string;
}

export interface CreateCreditCardApplyEventPayloadReturn {
  event_data: {
    action: ActionType.APPLY;
    component_type: ComponentType.CARD;
    custom_data: string;
    item_id: string;
    item_name: string;
    item_type: string;
    provider: string;
  };
}

export const createCreditCardApplyEventPayload = ({
  customData,
  itemId,
  itemName,
  itemType,
  provider,
}: CreateCreditCardApplyEventPayloadOptions): CreateCreditCardApplyEventPayloadReturn => {
  return {
    event_data: {
      action: ActionType.APPLY,
      component_type: ComponentType.CARD,
      custom_data: customData,
      item_id: itemId,
      item_name: itemName,
      item_type: itemType,
      provider,
    },
  };
};
