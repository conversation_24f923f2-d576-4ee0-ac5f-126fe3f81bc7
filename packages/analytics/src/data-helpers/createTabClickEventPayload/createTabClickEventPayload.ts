import type { ComponentType } from '../../enums';
import { ActionType, ItemType } from '../../enums';

export interface CreateTabClickEventPayloadOptions {
  componentType: ComponentType;
  componentVariant: string;
  pageType: string;
}

export interface CreateTabClickEventPayloadReturn {
  event_data: {
    action: ActionType;
    component_type: ComponentType;
    component_variant: string;
    item_type: ItemType;
    page_type: string;
    url: string;
  };
}

export const createTabClickEventPayload = ({
  componentType,
  componentVariant,
  pageType,
}: CreateTabClickEventPayloadOptions): CreateTabClickEventPayloadReturn => {
  return {
    event_data: {
      action: ActionType.CLICK,
      item_type: ItemType.BUTTON,
      component_type: componentType,
      page_type: pageType,
      component_variant: componentVariant,
      url: window.location.href,
    },
  };
};
