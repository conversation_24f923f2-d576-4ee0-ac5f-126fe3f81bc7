/**
 * @jest-environment jsdom
 */

import { ComponentType } from '../../enums';
import { createTabClickEventPayload } from './createTabClickEventPayload';

Object.defineProperty(window, 'location', {
  value: {
    href: 'https://localhost:8000/qantas/holidays/search/list',
  },
  writable: true,
});

describe('createTabClickEventPayload', () => {
  it('creates a valid tab click event payload', () => {
    const result = createTabClickEventPayload({
      componentType: ComponentType.FILTER_SORT_MODAL,
      componentVariant: 'sort',
      pageType: 'search',
    });

    expect(result).toStrictEqual({
      event_data: {
        action: 'click',
        component_type: 'filter_sort_modal',
        component_variant: 'sort',
        page_type: 'search',
        item_type: 'button',
        url: 'https://localhost:8000/qantas/holidays/search/list',
      },
    });
  });
});
