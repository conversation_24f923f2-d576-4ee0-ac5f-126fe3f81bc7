import { ActionType, ComponentType } from '../../enums';

export interface CreateCreditCardAddEventPayloadOptions {
  itemId: string;
  itemName: string;
  itemType: string;
  provider: string;
}

export interface CreateCreditCardAddEventPayloadReturn {
  event_data: {
    action: ActionType.ADD;
    component_type: ComponentType.CARD;
    item_id: string;
    item_name: string;
    item_type: string;
    provider: string;
  };
}

export const createCreditCardAddEventPayload = ({
  itemId,
  itemName,
  itemType,
  provider,
}: CreateCreditCardAddEventPayloadOptions): CreateCreditCardAddEventPayloadReturn => {
  return {
    event_data: {
      action: ActionType.ADD,
      component_type: ComponentType.CARD,
      item_id: itemId,
      item_name: itemName,
      item_type: itemType,
      provider,
    },
  };
};
