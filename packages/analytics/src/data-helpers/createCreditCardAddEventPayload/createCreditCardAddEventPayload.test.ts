import { ActionType, ComponentType } from '../../enums';
import { createCreditCardAddEventPayload } from './createCreditCardAddEventPayload';

describe('createCreditCardAddEventPayload', () => {
  it('creates a valid credit_card_add event payload', () => {
    const result = createCreditCardAddEventPayload({
      itemId: '123',
      itemName: 'Credit Card',
      itemType: 'credit_card',
      provider: 'provider',
    });

    expect(result).toStrictEqual({
      event_data: {
        action: ActionType.ADD,
        component_type: ComponentType.CARD,
        item_id: '123',
        item_name: 'Credit Card',
        item_type: 'credit_card',
        provider: 'provider',
      },
    });
  });
});
