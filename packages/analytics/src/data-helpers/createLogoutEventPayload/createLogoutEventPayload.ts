import { ActionType } from '../../enums';
import { createUserPayload } from '../createUserPayload';

export interface CreateLogoutEventPayloadOptions {
  pageType?: string;
  userQffHash: string;
}

export interface CreateLogoutEventPayloadReturn {
  event_data: {
    action: ActionType;
    page_type?: string;
  };
  user: Omit<ReturnType<typeof createUserPayload>, 'user_points'>;
}

export const createLogoutEventPayload = ({
  pageType,
  userQffHash,
}: CreateLogoutEventPayloadOptions): CreateLogoutEventPayloadReturn => {
  const user = createUserPayload({
    userId: userQffHash, // keep it same as qffHash to align with the loyalty and qantas.com ecosystem
    qffHashId: userQffHash,
    isLoggedIn: false,
    qffPoints: undefined,
  });
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { user_points, ...userWithoutPoints } = user;
  return {
    event_data: {
      action: ActionType.LOGOUT,
      page_type: pageType,
    },
    user: userWithoutPoints,
  };
};
