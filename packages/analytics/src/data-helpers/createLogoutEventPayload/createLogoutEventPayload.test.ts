import { ActionType } from '../../enums';
import { createLogoutEventPayload } from './createLogoutEventPayload';

describe('createLogoutEventPayload', () => {
  const payload = {
    pageType: 'search',
    userQffHash: 'abcde',
  };

  const result = createLogoutEventPayload(payload);

  it('creates a valid logout payload when all fields are populated', () => {
    expect(result).toStrictEqual({
      event_data: {
        action: ActionType.LOGOUT,
        page_type: 'search',
      },
      user: {
        user_id: 'abcde',
        user_login_status: 'logged out',
        user_qff_hash: 'abcde',
      },
    });
  });
});
