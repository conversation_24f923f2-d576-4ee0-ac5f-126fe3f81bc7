import { ActionType, ComponentType } from '../../enums';

export interface CreatePaymentTypeSelectEventPayloadOptions {
  userPaymentType: string;
}

export interface CreatePaymentTypeSelectEventPayloadReturn {
  event_data: {
    action: ActionType.SELECT;
    component_type: ComponentType.USER_PAYMENT_TYPE;
    user_payment_type: string;
  };
}

export const createPaymentTypeSelectEventPayload = ({
  userPaymentType,
}: CreatePaymentTypeSelectEventPayloadOptions): CreatePaymentTypeSelectEventPayloadReturn => {
  return {
    event_data: {
      action: ActionType.SELECT,
      component_type: ComponentType.USER_PAYMENT_TYPE,
      user_payment_type: userPaymentType,
    },
  };
};
