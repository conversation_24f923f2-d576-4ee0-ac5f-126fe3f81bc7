import { ActionType, ComponentType } from '../../enums';
import { createPaymentTypeSelectEventPayload } from './createPaymentTypeSelectEventPayload';

describe('createPaymentTypeSelectEventPayload', () => {
  it('creates a valid payment_type_select event payload', () => {
    const result = createPaymentTypeSelectEventPayload({
      userPaymentType: 'credit_card',
    });

    expect(result).toStrictEqual({
      event_data: {
        action: ActionType.SELECT,
        component_type: ComponentType.USER_PAYMENT_TYPE,
        user_payment_type: 'credit_card',
      },
    });
  });
});
