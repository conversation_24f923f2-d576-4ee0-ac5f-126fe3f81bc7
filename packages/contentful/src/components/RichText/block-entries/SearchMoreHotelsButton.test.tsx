import { fireEvent, render } from '@testing-library/react';

import { sendCtaClickEvent } from '../../../analytics/sendCtaClickEvent';
import { SearchMoreHotelsButton } from './SearchMoreHotelsButton';

jest.mock('../../../analytics/sendCtaClickEvent', () => ({
  sendCtaClickEvent: jest.fn(),
}));

describe('SearchMoreHotelsButton', () => {
  it('should render the button with correct text and URL', () => {
    const url = 'https://example.com/search';
    const { getByText } = render(<SearchMoreHotelsButton url={url} />);

    const button = getByText('Search more hotels');

    expect(button).toHaveAttribute('href', url);
  });

  it('should call sendCtaClickEvent on click', () => {
    const url = 'https://example.com/search';
    const { getByText } = render(<SearchMoreHotelsButton url={url} />);

    const button = getByText('Search more hotels');
    fireEvent.click(button);

    expect(sendCtaClickEvent).toHaveBeenCalledWith({
      itemText: 'Search more hotels',
      itemType: 'button',
      url,
    });
  });

  it('should render URL without utm tags', () => {
    const url = 'https://example.com/search?utm_source=test&utm_medium=test&location=Sydney';
    const { getByText } = render(<SearchMoreHotelsButton url={url} />);

    const button = getByText('Search more hotels');
    expect(button).toHaveAttribute('href', 'https://example.com/search?location=Sydney');
  });
});
