// FIXME:
/* eslint-disable @typescript-eslint/no-unnecessary-condition */

import Link from 'next/link';
import pluralize from 'pluralize';

import { getDestinationProperties } from '@qantasexperiences/utils/api';
import { removeUtmParams } from '@qantasexperiences/utils/formatting';
import { cn } from '@qantasexperiences/utils/tailwind';

type SimpleCardProps = Omit<React.ComponentProps<typeof Link>, 'children'> & {
  subtitle?: string | undefined;
  title: string;
};

export const SimpleCard = ({ title, subtitle, className, ...linkProps }: SimpleCardProps) => {
  return (
    <Link
      {...linkProps}
      className={cn(
        'hover:shadow-floating flex items-center justify-between gap-2 bg-white px-5 py-3 shadow transition-shadow',
        className,
      )}
    >
      <div className="min-w-0 text-base">
        <div className="truncate text-[1.125rem]">{title}</div>
        {subtitle && <div className="truncate text-neutral-600">{subtitle}</div>}
      </div>
      {/* TODO: move somewhere else */}
      <svg aria-hidden className="text-red-600" viewBox="0 0 24 24" width="24" height="24">
        <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path>
      </svg>
    </Link>
  );
};

interface MerchandisingNavigationProps {
  regionId: string | undefined;
  title: string;
}

export const MerchandisingNavigation = async ({
  title,
  regionId,
}: MerchandisingNavigationProps) => {
  const data = await getDestinationProperties(regionId);

  if (!data?.subRegions || data.subRegions?.length === 0) return null;

  return (
    <div className="rt-contained" data-ref="qec-merchandising-navigation">
      <h2>{title}</h2>
      <ul className="grid-cols-auto-fill-[21.5rem] grid gap-2">
        {data.subRegions?.map((i) => (
          <li key={i?.regionName}>
            <SimpleCard
              href={removeUtmParams(i?.regionAvailabilitySearchUrl)}
              title={i.regionName}
              subtitle={
                'totalProperties' in i
                  ? `${i.totalProperties} ${pluralize('property', i.totalProperties)}`
                  : undefined
              }
            />
          </li>
        ))}
      </ul>
    </div>
  );
};
