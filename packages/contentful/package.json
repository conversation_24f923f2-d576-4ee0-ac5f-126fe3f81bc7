{"name": "@qantasexperiences/contentful", "private": true, "version": "0.1.0", "type": "module", "exports": {".": "./src/index.ts", "./components": "./src/components/index.ts", "./queries": "./src/queries/index.ts", "./schemas": "./src/schemas/index.ts"}, "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "typecheck": "tsc --noEmit", "test:unit": "jest", "test:unit:ci": "jest", "test:unit:watch": "jest --watch"}, "devDependencies": {"@next/third-parties": "^15.4.4", "@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/jest": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@qantasexperiences/ui": "workspace:*", "@qantasexperiences/utils": "workspace:*", "@storybook/react-vite": "^9.0.18", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^22.16.5", "@types/pluralize": "^0.0.33", "@types/react": "^19.1.8", "jest": "^30.0.5", "next": "^15.4.4", "react": "^19.1.0", "storybook": "^9.0.18", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}, "prettier": "@qantasexperiences/code-style/prettier", "dependencies": {"@contentful/rich-text-react-renderer": "^16.1.0", "@contentful/rich-text-types": "^17.1.0", "@qantasexperiences/analytics": "workspace:*", "@qantasexperiences/env": "workspace:*", "@qantasexperiences/logger": "workspace:*", "@qantasexperiences/optimizely": "workspace:*", "@qantasexperiences/tenants": "workspace:*", "@qantasexperiences/utils": "workspace:*", "@radix-ui/react-accordion": "^1.2.11", "camelcase-keys": "^9.1.3", "pluralize": "^8.0.0", "react-aria-components": "^1.10.1", "zod": "^3.25.67"}, "peerDependencies": {"@next/third-parties": "^14.0 || ^15.0", "next": "^14.0 || ^15.0", "react": "^19.0"}}