{"name": "@qantasexperiences/auth", "private": false, "version": "0.5.0", "type": "module", "exports": {"./client": "./src/client/index.ts", "./server": "./src/server/index.ts", "./constants": "./src/constants.ts"}, "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "typecheck": "tsc --noEmit", "test:unit": "jest", "test:unit:ci": "jest", "test:unit:watch": "jest --watch"}, "dependencies": {"@qantasexperiences/env": "workspace:*", "@qantasexperiences/tenants": "workspace:*", "@qantasexperiences/utils": "workspace:*", "camelcase-keys": "^9.1.3", "ioredis": "5.6.1", "ky": "1.7.5", "lodash": "^4.17.21", "server-only": "^0.0.1", "zod": "^3.25.67"}, "dependenciesComments": {"ky": "Version locked to 1.7.5 as 1.8.1 requires node v22"}, "peerDependencies": {"next": "^14.0 || ^15.0", "react": "^19.0"}, "devDependencies": {"@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/jest": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.19", "@types/react": "^19.1.8", "jest": "^30.0.5", "jest-fixed-jsdom": "^0.0.9", "msw": "^2.10.4", "next": "^15.4.4", "react": "^19.1.0", "typescript": "^5.8.3"}, "prettier": "@qantasexperiences/code-style/prettier", "beachball": {"shouldPublish": false}, "publishConfig": {"registry": "https://repositories.services.jqdev.net/repository/npm-local/"}}