'use client';

import { useCallback } from 'react';
import { useRouter } from 'next/navigation';

import { useAuthContext } from './AuthProvider';

export const useLogout = () => {
  const router = useRouter();
  const {
    logout: { ssoUrl, redirectTo, onLogoutSuccess },
    qhAPIBase,
  } = useAuthContext();

  const logout = useCallback(async (): Promise<void> => {
    const rerenderServerComponents = () => router.refresh();

    const qhLogout = fetch(`${qhAPIBase}/logout`, {
      credentials: 'include',
      method: 'POST',
    });

    const ssoLogout = fetch(ssoUrl, {
      credentials: 'include',
    });

    const [response] = await Promise.all([ssoLogout, qhLogout]);

    if (!response.ok) {
      // TODO: log sso error to sentry
      return;
    }

    onLogoutSuccess?.();

    if (redirectTo) {
      window.location.href = redirectTo;
    } else {
      rerenderServerComponents();
    }
  }, [qhAPIBase, ssoUrl, redirectTo, router, onLogoutSuccess]);

  return logout;
};
