import type { ReactNode } from 'react';
import { useRouter } from 'next/navigation';

import { renderHookWithTenant } from '@qantasexperiences/tenants/react/testing';

import { AuthProvider } from './AuthProvider';
import { useLogout } from './useLogout';

jest.mock('next/navigation');

const clientId = 'test-client-id';
const qhAPIBase = 'https://api.test.com';
const login = {
  ssoUrl: 'https://sso.com/login',
  getRedirectPath: (pathname: string): string => pathname,
};
const logout = {
  ssoUrl: 'https://sso.com/logout',
  redirectTo: '/home',
};

const wrapperWithRedirect = ({ children }: { children: ReactNode }) => (
  <AuthProvider clientId={clientId} qhAPIBase={qhAPIBase} login={login} logout={logout}>
    {children}
  </AuthProvider>
);

const wrapperWithoutRedirect = ({ children }: { children: ReactNode }) => (
  <AuthProvider
    clientId={clientId}
    qhAPIBase={qhAPIBase}
    login={login}
    logout={{ ...logout, redirectTo: undefined }}
  >
    {children}
  </AuthProvider>
);

const { fetch } = global;

describe('useLogout', () => {
  Object.defineProperty(window, 'location', {
    value: { href: 'http://localhost:3000' },
    writable: true,
  });

  beforeEach(() => {
    global.fetch = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    global.fetch = fetch;
    window.location.href = location.href;
  });

  it('calls both SSO and QH logout', async () => {
    global.fetch = jest.fn().mockResolvedValue({ ok: true });

    const { result } = renderHookWithTenant(() => useLogout(), {
      brand: 'qantas',
      channel: 'hotels',
      wrapper: wrapperWithRedirect,
    });

    await result.current();

    expect(global.fetch).toHaveBeenCalledTimes(2);
    expect(global.fetch).toHaveBeenCalledWith(logout.ssoUrl, {
      credentials: 'include',
    });
    expect(global.fetch).toHaveBeenCalledWith(`${qhAPIBase}/logout`, {
      credentials: 'include',
      method: 'POST',
    });
  });

  it('calls router refresh in order to update server components on successful logout', async () => {
    const refresh = jest.fn();
    // @ts-expect-error it wants a full implementation of `useRouter` but we only care about refresh
    jest.mocked(useRouter).mockReturnValue({ refresh });
    global.fetch = jest.fn().mockResolvedValue({ ok: true });

    const { result } = renderHookWithTenant(() => useLogout(), {
      brand: 'qantas',
      channel: 'hotels',
      wrapper: wrapperWithoutRedirect,
    });

    await result.current();

    expect(refresh).toHaveBeenCalledTimes(1);
  });

  it('does not call router refresh if a redirect route is provided', async () => {
    const refresh = jest.fn();
    // @ts-expect-error it wants a full implementation of `useRouter` but we only care about refresh
    jest.mocked(useRouter).mockReturnValue({ refresh });
    global.fetch = jest.fn().mockResolvedValue({ ok: true });

    const { result } = renderHookWithTenant(() => useLogout(), {
      brand: 'qantas',
      channel: 'hotels',
      wrapper: wrapperWithRedirect,
    });

    await result.current();

    expect(refresh).not.toHaveBeenCalled();
  });

  it('redirects to the specified redirect URL when logout is successful', async () => {
    global.fetch = jest.fn().mockResolvedValue({ ok: true });

    const { result } = renderHookWithTenant(() => useLogout(), {
      brand: 'qantas',
      channel: 'hotels',
      wrapper: wrapperWithRedirect,
    });

    await result.current();

    expect(window.location.href).toBe('/home');
  });

  it('calls onLogoutSuccess with user data when logout is successful', async () => {
    const onLogoutSuccess = jest.fn();
    global.fetch = jest.fn().mockResolvedValue({ ok: true });

    const { result } = renderHookWithTenant(() => useLogout(), {
      brand: 'qantas',
      channel: 'hotels',
      wrapper: ({ children }: { children: ReactNode }) => (
        <AuthProvider
          clientId={clientId}
          qhAPIBase={qhAPIBase}
          login={login}
          logout={{ ...logout, onLogoutSuccess }}
        >
          {children}
        </AuthProvider>
      ),
    });

    await result.current();

    expect(onLogoutSuccess).toHaveBeenCalled();
  });
});
