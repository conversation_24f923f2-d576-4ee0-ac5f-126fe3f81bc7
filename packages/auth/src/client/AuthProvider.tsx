'use client';

import type { ReactNode } from 'react';

import { createCtx } from '@qantasexperiences/utils/context';

interface LoginConfig {
  getRedirectPath?: (pathname: string) => string;
  ssoUrl: string;
}

interface LogoutConfig {
  onLogoutSuccess?: () => void;
  redirectTo?: string;
  ssoUrl: string;
}

interface AuthConfig {
  clientId: string;
  login: LoginConfig;
  logout: LogoutConfig;
  qhAPIBase: string;
}

const [useAuthContext, BaseAuthProvider] = createCtx<AuthConfig>();

interface AuthProviderProps extends AuthConfig {
  children: ReactNode;
}

const AuthProvider = ({ children, clientId, qhAPIBase, login, logout }: AuthProviderProps) => {
  return (
    <BaseAuthProvider
      value={{
        clientId,
        qhAPIBase,
        login,
        logout,
      }}
    >
      {children}
    </BaseAuthProvider>
  );
};

export { useAuthContext, AuthProvider };
