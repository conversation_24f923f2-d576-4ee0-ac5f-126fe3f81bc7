import type { ReactNode } from 'react';
import { render, renderHook, screen } from '@testing-library/react';

import { AuthProvider, useAuthContext } from './AuthProvider';

const clientId = 'test-client-id';
const qhAPIBase = 'https://api.test.com';
const login = {
  ssoUrl: 'https://sso.com/login',
  getRedirectPath: (pathname: string): string => pathname,
};
const logout = {
  redirectTo: '/home',
  ssoUrl: 'https://sso.com/logout',
  onLogoutSuccess: jest.fn(),
};

describe('AuthProvider', () => {
  it('renders children', () => {
    render(
      <AuthProvider clientId={clientId} qhAPIBase={qhAPIBase} login={login} logout={logout}>
        <div>children</div>
      </AuthProvider>,
    );

    expect(screen.getByText('children')).toBeInTheDocument();
  });

  it('returns auth context', () => {
    const { result } = renderHook(() => useAuthContext(), {
      wrapper: ({ children }: { children: ReactNode }) => (
        <AuthProvider clientId={clientId} qhAPIBase={qhAPIBase} login={login} logout={logout}>
          {children}
        </AuthProvider>
      ),
    });

    expect(result.current).toEqual({
      clientId,
      login,
      logout,
      qhAPIBase,
    });
  });
});
